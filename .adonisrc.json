{"typescript": true, "commands": ["./commands", "@adonisjs/core/build/commands/index.js", "@adonisjs/repl/build/commands", "@adonisjs/lucid/build/commands", "adonis-lucid-filter/build/commands", "@adonisjs/mail/build/commands", "adonis5-scheduler/build/commands"], "exceptionHandlerNamespace": "App/Exceptions/Handler", "aliases": {"App": "app", "Config": "config", "Database": "database", "Contracts": "contracts"}, "preloads": ["./start/routes", "./start/kernel", "./start/cron", {"file": "./start/validator", "environment": ["web"]}], "providers": ["./providers/AppProvider", "@adonisjs/core", "@adonisjs/session", "@adonisjs/view", "@adonisjs/shield", "@adonisjs/lucid", "@adonisjs/auth", "@adonisjs/lucid-slugify", "adonis-lucid-filter", "@adonisjs/mail", "adonis5-scheduler"], "metaFiles": [{"pattern": "public/**", "reloadServer": false}, {"pattern": "resources/views/**/*.edge", "reloadServer": false}], "aceProviders": ["@adonisjs/repl"]}