<!DOCTYPE html>
<html>
  <head>
    <meta name="viewport" content="width=device-width" />
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
    <title>{{title}}</title>
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin="" />
    <link
      href="https://fonts.googleapis.com/css2?family=Montserrat:wght@400;700&display=swap"
      rel="stylesheet"
    />
    <style>
        body {
          max-width: 600px;
        }

        .bg {
          background-size: contain;
        }

        .bg h2 {
          padding-top: 60px;
        }

        .radio {
            display: flex;
            justify-content: start;
            align-items: center;
        }

        table[class="body"] h1 {
          font-size: 28px !important;
          margin-bottom: 10px !important;
        }

        table[class="body"] a,
        table[class="body"] ol,
        table[class="body"] p,
        table[class="body"] span,
        table[class="body"] td,
        table[class="body"] ul {
          font-size: 16px !important;
        }

        table[class="body"] .article,
        table[class="body"] .wrapper {
          padding: 10px !important;
        }

        table[class="body"] .content {
          padding: 0 !important;
        }

        table[class="body"] .container {
          padding: 0 !important;
          width: 100% !important;
        }

        table[class="body"] .main {
          border-left-width: 0 !important;
          border-radius: 0 !important;
          border-right-width: 0 !important;
        }

        table[class="body"] .btn table {
          width: 100% !important;
        }

        table[class="body"] .btn a {
          width: 100% !important;
        }

        table[class="body"] .img-responsive {
          height: auto !important;
          max-width: 100% !important;
          width: auto !important;
        }
      @media all {
        .ExternalClass {
          width: 100%;
        }

        .ExternalClass,
        .ExternalClass div,
        .ExternalClass font,
        .ExternalClass p,
        .ExternalClass span,
        .ExternalClass td {
          line-height: 100%;
        }

        .apple-link a {
          color: inherit !important;
          font-family: inherit !important;
          font-size: inherit !important;
          font-weight: inherit !important;
          line-height: inherit !important;
          text-decoration: none !important;
        }

        #MessageViewBody a {
          color: inherit;
          text-decoration: none;
          font-size: inherit;
          font-family: inherit;
          font-weight: inherit;
          line-height: inherit;
        }

        .btn-primary {
          font-family: "Montserrat", sans-serif;
          font-weight: bold;
          background-color: #ffc20c;
          color: white;
          border: none;
          text-decoration: none;
          padding: 0.5em 1em;
        }

        .btn-primary table td:hover {
          background-color: #34495e !important;
        }

        .btn-primary a:hover {
          background-color: #34495e !important;
          border-color: #34495e !important;
        }

        .m-0 {
          margin: 0;
        }
      }
    </style>
  </head>
  <body
    style="
      background-color: #f6f6f6;
      font-family: 'Montserrat', sans-serif;
      -webkit-font-smoothing: antialiased;
      font-size: 14px;
      line-height: 1.4;
      padding: 0;
      -ms-text-size-adjust: 100%;
      -webkit-text-size-adjust: 100%;
      max-width: 600px;
      margin: auto;
    "
  >
    <br />
    <div
      class="content"
      style="
        padding: 10px;
        font-family: 'Montserrat', sans-serif;
        font-size: 13px;
        font-weight: normal;
        font-stretch: normal;
        font-style: normal;
        line-height: 1.38;
        letter-spacing: normal;
        color: #3a3838;
      "
    >
      <h3 class="m-0" style="text-align: center;">
        Hi {{username ? username : email}},
      </h3>

      <br />

      <h3 class="m-0" style="text-align: center;">
        Thank you for shopping with Casefinite recently - we hope you were happy with your purchase.
      </h3>

      <br/>

      @each(product in products)

      <h3 class="m-0" style="text-align: center;">
        We'd love to hear what you think about your 
        <a href="{{product.url}}">{{product.name}}</a>.
      </h3>

      <br/>

      <form method="POST" action={{product.post_url}} onsubmit="return false">

        <h3>Rate: </h3>
        <div class='radio'>
            <input type="radio" name="score" value="5" /> 
            <span>Great</span>
        </div>

        <div class='radio'>
            <input type="radio" name="score" value="5" /> 
            <span>Good</span>
        </div>
        
        <div class='radio'>
            <input type="radio" name="score" value="5" /> 
            <span>Okay</span>
        </div>

        <div class='radio'>
            <input type="radio" name="score" value="5" /> 
            <span>Bad</span>
        </div>

        <div class='radio'>
            <input type="radio" name="score" value="5" /> 
            <span>Terrible</span>
        </div>
        
        <br/>
        <h3>Review: </h3>
        <textarea style="width: 100%;" name="content"></textarea>

        <br/>
        <h3>Review Title: </h3>
        <input name="title" style="width: 100%" />

        <br/>
        <button type="submit">Submit Your Review</button>
    </form>

      @end
    
    </div>
  </body>
</html>
