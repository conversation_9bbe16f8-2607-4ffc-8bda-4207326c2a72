<!DOCTYPE html>
<html>
  <head>
    <meta name="viewport" content="width=device-width" />
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
    <title>{{title}}</title>
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin="" />
    <link
      href="https://fonts.googleapis.com/css2?family=Montserrat:wght@400;700&display=swap"
      rel="stylesheet"
    />
    <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
    <style>
      body {
        max-width: 600px;
      }

      .bg {
        background-size: contain;
      }

      .bg h2 {
        padding-top: 60px;
      }

      .radio {
          display: flex;
          justify-content: start;
          align-items: center;
      }

      table[class="body"] h1 {
        font-size: 28px !important;
        margin-bottom: 10px !important;
      }

      table[class="body"] a,
      table[class="body"] ol,
      table[class="body"] p,
      table[class="body"] span,
      table[class="body"] td,
      table[class="body"] ul {
        font-size: 16px !important;
      }

      table[class="body"] .article,
      table[class="body"] .wrapper {
        padding: 10px !important;
      }

      table[class="body"] .content {
        padding: 0 !important;
      }

      table[class="body"] .container {
        padding: 0 !important;
        width: 100% !important;
      }

      table[class="body"] .main {
        border-left-width: 0 !important;
        border-radius: 0 !important;
        border-right-width: 0 !important;
      }

      table[class="body"] .btn table {
        width: 100% !important;
      }

      table[class="body"] .btn a {
        width: 100% !important;
      }

      table[class="body"] .img-responsive {
        height: auto !important;
        max-width: 100% !important;
        width: auto !important;
      }
    @media all {
      .ExternalClass {
        width: 100%;
      }

      .ExternalClass,
      .ExternalClass div,
      .ExternalClass font,
      .ExternalClass p,
      .ExternalClass span,
      .ExternalClass td {
        line-height: 100%;
      }

      .apple-link a {
        color: inherit !important;
        font-family: inherit !important;
        font-size: inherit !important;
        font-weight: inherit !important;
        line-height: inherit !important;
        text-decoration: none !important;
      }

      #MessageViewBody a {
        color: inherit;
        text-decoration: none;
        font-size: inherit;
        font-family: inherit;
        font-weight: inherit;
        line-height: inherit;
      }

      .btn-primary {
        margin-top: 10px;
        font-family: "Montserrat", sans-serif;
        font-weight: bold;
        background-color: #ffc20c;
        color: white;
        border: none;
        text-decoration: none;
        padding: 0.5em 1em;
      }

      .btn-primary table td:hover {
        cursor: pointer;
        background-color: #34495e !important;
      }

      .btn-primary a:hover {
        background-color: #34495e !important;
        border-color: #34495e !important;
      }

      .m-0 {
        margin: 0;
      }
    }
  </style>
  </head>
  <body>
    <div>
      <div>  
        <div style="background: #f0f2f5; width: auto; margin-top: 5px; padding-left: 4px; padding-right: 4px">
          @each(history in histories)
            <div style="padding: 4px">
                @if(history.adminId)
                <p style="font-weight: bold">Casefinite Customer service : </p>
                @else
                <p style="font-weight: bold">You : </p>
                @end
                {{history.content}}
            </div>
            <hr/>
          @end 
        </div>
    </div>
      
      <br/>
      <p style="text-align: left">
        {{content}}
      </p>
      <br/>
      <a href="http://localhost:3000/reply/{{id}}?token={{token}}"><button class='btn-primary' type="button" name="submitButton">Click here to reply</button></a>
    </div>

  </body>
</html>
