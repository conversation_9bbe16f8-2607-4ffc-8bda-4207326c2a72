# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview
This is a **Node.js backend API** built with **AdonisJS v5** (TypeScript) for CASEFINITE Japan - an e-commerce order management system. The application integrates with Shopify, handles order fulfillment, customer management, reviews, and provides admin functionality.

## Development Commands

### Core Commands
- **Development**: `pnpm dev` - Start server with hot reload
- **Build**: `pnpm build` - Compile TypeScript to JavaScript for production
- **Production**: `pnpm start` - Run compiled server (after build)
- **Type Checking**: `node ace type-check` - Check TypeScript without compilation
- **Format**: `pnpm format` - Run Prettier on all files

### Database Commands
- **Run Migrations**: `node ace migration:run`
- **Rollback Migrations**: `node ace migration:rollback`
- **Seed Database**: `node ace db:seed`
- **Migration Status**: `node ace migration:status`

### Testing & Debugging
- **Run Tests**: `node ace test`
- **List Routes**: `node ace list:routes`
- **REPL**: `node ace repl` - Interactive console with application context
- **Generate Resources**:
  - Controller: `node ace make:controller [name]`
  - Model: `node ace make:model [name]`
  - Migration: `node ace make:migration [name]`
  - Route: Add to appropriate file in `app/Routes/`

## Architecture

### Tech Stack
- **Framework**: AdonisJS v5 (TypeScript)
- **Database**: MySQL with Lucid ORM
- **Authentication**: Adonis Auth with JWT
- **Queue**: Built-in scheduler for cron jobs
- **Storage**: AWS S3 compatible (DigitalOcean Spaces)
- **Email**: SendGrid integration
- **API**: RESTful with JSON responses

### Key Directories
- `app/Controllers/Http/` - REST API controllers
- `app/Models/` - Database models (Lucid ORM)
- `app/Routes/` - Modular route definitions
- `database/migrations/` - Database schema changes
- `database/seeders/` - Test data population
- `config/` - Application configuration
- `resources/views/` - Email templates (Edge.js)

### API Structure
**Base URL**: `/api/v1`

**Public Routes**:
- Authentication (`/auth/*`)
- Webhooks (`/webhooks/*`) - Shopify integration
- Customer endpoints (reviews, feedback, recruitment)

**Admin Routes** (`/admin/*`):
- Protected by auth middleware
- Full CRUD operations for all entities
- Reports and analytics endpoints

### Database Models
**Core Entities**:
- `Order` - Shopify orders with fulfillment tracking
- `Customer` - Customer information
- `Product`/`ProductVariant` - Shopify products
- `Stock` - Inventory management
- `Warehouse` - Storage locations
- `Fulfillment` - Order fulfillment tracking
- `Review` - Customer reviews
- `Feedback` - Customer feedback system

### Integration Points
- **Shopify**: Webhook handlers for real-time updates
- **Yamato/Sagawa**: Shipping integration
- **Yahoo APIs**: Additional e-commerce platform
- **Klaviyo**: Email marketing
- **Google Translate**: Multi-language support
- **Puppeteer**: Web scraping capabilities

### Environment Variables
Key `.env` variables:
- Database: `MYSQL_HOST`, `MYSQL_USER`, `MYSQL_PASSWORD`, `MYSQL_DB_NAME`
- Shopify: `SHOP`, `API_KEY`, `API_SECRET_KEY`, `ADMIN_ACCESS`
- Email: `SENDGRID_API_KEY`, `MAIL_USERNAME`
- Storage: `SPACES_KEY`, `SPACES_SECRET`
- Webhooks: `SHOPIFY_WEBHOOK_SECRET`

### Development Setup
1. **Install dependencies**: `pnpm install`
2. **Setup database**: Ensure MySQL is running with credentials from `.env`
3. **Run migrations**: `node ace migration:run`
4. **Seed data**: `node ace db:seed`
5. **Start development**: `pnpm dev`

### Testing Strategy
- No existing test files found - tests should be added in `tests/` directory
- Use `node ace make:test` to generate new test files
- Follow AdonisJS testing conventions with Japa test runner