import BaseSeeder from '@ioc:Adonis/Lucid/Seeder'
import Fulfillment from 'App/Models/Fulfillment'
import LineItem from 'App/Models/LineItem'
import Order from 'App/Models/Order'
import shopifyClient from 'App/Network/shopifyClient'
import Shopify from 'App/Models/Shopify/base'
import { AxiosResponse } from 'axios'
import _ from 'lodash'
import Database from '@ioc:Adonis/Lucid/Database'
import { Exception } from '@adonisjs/core/build/standalone'
import FulfillmentLineItem from 'App/Models/FulfillmentLineItem'

export default class OrderFulfillmentSeeder extends BaseSeeder {
  public async run() {
    // get all orders of previous db format
    var orders: Order[] = []
    const limit = 250
    var offset = 619

    do {
      orders = await Order.query()
        .limit(limit)
        .offset(offset)
        .orderBy('id', 'asc')
        .preload('event', (query) => {
          query.preload('stock', (query) => {
            query.preload('variant', (query) => {
              query.preload('product')
            })
          })
        })
      const orderIds = orders.map((item) => item.platformOrderId)

      const {
        data: { orders: shopifyOrders },
      }: AxiosResponse<{ orders: Shopify.Order[] }> = await shopifyClient.get(
        `/orders.json?ids=${orderIds.join(',')}&status=any&limit=${limit}`
      )

      // TODO: add TRX
      for (const order of orders) {
        const shopifyOrder = _.find(shopifyOrders, { id: parseFloat(order.platformOrderId) })
        if (!shopifyOrder) {
          continue
        }
        console.log('processing order : ' + order.id)

        try {
          await Database.transaction(async (trx) => {
            try {
              order.merge({
                cancelledAt: shopifyOrder.cancelled_at ?? undefined,
                orderStatusId: order.fulfillmentStatusId,
                shopPlatformId: 1,
                name: order.name ?? shopifyOrder.name,
              })
              await order.useTransaction(trx).save()

              // create a fulfillment for each order
              var fulfillment: null | Fulfillment = null

              if (order.fulfillmentStatusId === 5) {
                const shopifyFulfillment = _.first(shopifyOrder.fulfillments)
                fulfillment = await Fulfillment.updateOrCreate(
                  {
                    orderId: order.id,
                  },
                  {
                    name: shopifyFulfillment?.name,
                    trackingNumber: shopifyFulfillment?.tracking_number,
                    trackingCompanyName: shopifyFulfillment?.tracking_company,
                    trackingUrl: shopifyFulfillment?.tracking_url,
                    createdAt: order.fulfilledAt ?? order.createdAt,
                    updatedAt: order.fulfilledAt ?? order.updatedAt,
                    fulfillmentStatusId: 2,
                  },
                  {
                    client: trx,
                  }
                )
              }

              if (shopifyOrder.line_items?.length > 0) {
                // create line items for each item in orders
                for (const lineItem of shopifyOrder.line_items) {
                  const localEvent = order.event.find(
                    (item) => item.stock.variant.sku === lineItem.sku
                  )
                  if (localEvent) {
                    //if previous fulfillment status was shipped, change event to success
                    order.fulfillmentStatusId === 5 ? (localEvent.statusId = 1) : null
                    //if previous fulfillment status was cancelled, change event to failed
                    order.fulfillmentStatusId === 6 ? (localEvent.statusId = 3) : null
                    await localEvent?.save()
                  }

                  const localLineItem = await LineItem.updateOrCreate(
                    {
                      // eventId: localEvent?.id ?? undefined,
                      sku: localEvent?.stock?.variant?.sku,
                      orderId: order.id,
                    },
                    {
                      // ...lineItem,
                      name: lineItem.name,
                      platformLineItemId: lineItem.id.toString(),
                      price: parseFloat(lineItem.price),
                      quantity: Math.abs(localEvent?.quantity ?? 0),
                      grams: localEvent?.stock?.variant?.weight,
                      taxable: lineItem.taxable,
                      properties: lineItem.properties,
                      requiresShipping: lineItem.requires_shipping,
                      vendor: lineItem.vendor,
                      giftCard: lineItem.gift_card,
                      productTitle: lineItem.title,
                      variantTitle: lineItem.variant_title,
                      // fulfillableQuantity: lineItem.fulfillable_quantity,
                      taxLines: lineItem.tax_lines,
                      duties: lineItem.duties,
                      // fulfillmentId: fulfillment?.id,
                    },
                    {
                      client: trx,
                    }
                  )

                  if (fulfillment) {
                    await FulfillmentLineItem.updateOrCreate(
                      {
                        fulfillmentId: fulfillment?.id,
                        eventId: localEvent?.id,
                        lineItemId: localLineItem?.id,
                      },
                      {
                        fulfillmentId: fulfillment?.id,
                        eventId: localEvent?.id,
                        lineItemId: localLineItem?.id,
                      },
                      {
                        client: trx,
                      }
                    )
                  }
                }
              }
            } catch (e) {
              throw new Exception(e)
            }
          })
        } catch (e) {
          console.log(e)
        }
      }

      offset += limit
    } while (orders.length > 0)
  }
}
