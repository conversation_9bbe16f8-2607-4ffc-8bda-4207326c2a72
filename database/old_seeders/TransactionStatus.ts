import BaseSeeder from '@ioc:Adonis/Lucid/Seeder'
import TransactionStatus from 'App/Models/TransactionStatus'

export default class TransactionStatusSeeder extends BaseSeeder {
  public async run() {
    // Write your database queries inside the run method
    await TransactionStatus.updateOrCreate(
      { id: 1 },
      {
        status: 'authorized',
      }
    )

    await TransactionStatus.updateOrCreate(
      { id: 2 },
      {
        status: 'pending',
      }
    )

    await TransactionStatus.updateOrCreate(
      { id: 3 },
      {
        status: 'paid',
      }
    )

    await TransactionStatus.updateOrCreate(
      { id: 4 },
      {
        status: 'cancelled',
      }
    )

    await TransactionStatus.updateOrCreate(
      { id: 5 },
      {
        status: 'refunded',
      }
    )

    await TransactionStatus.updateOrCreate(
      { id: 6 },
      {
        status: 'partially refunded',
      }
    )
  }
}
