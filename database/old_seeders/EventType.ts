import BaseSeeder from '@ioc:Adonis/Lucid/Seeder'
import EventType from 'App/Models/EventType'

export default class EventTypeSeeder extends BaseSeeder {
  public async run() {
    // Write your database queries inside the run method
    EventType.updateOrCreate(
      { id: 1 },
      {
        isIn: true,
        eventName: 'purchase',
      }
    )

    await EventType.updateOrCreate(
      { id: 2 },
      {
        isIn: true,
        eventName: 'return',
      }
    )

    await EventType.updateOrCreate(
      { id: 3 },
      {
        isIn: false,
        eventName: 'sold',
      }
    )

    await EventType.updateOrCreate(
      { id: 4 },
      {
        isIn: false,
        eventName: 'sendToFBA',
      }
    )

    await EventType.updateOrCreate(
      { id: 5 },
      {
        isIn: false,
        eventName: 'replacement',
      }
    )

    await EventType.updateOrCreate(
      { id: 6 },
      {
        isIn: false,
        eventName: 'disposal',
      }
    )

    await EventType.updateOrCreate(
      { id: 7 },
      {
        isIn: false,
        eventName: 'shopifyAdjustment',
      }
    )

    await EventType.updateOrCreate(
      { id: 8 },
      {
        isIn: true,
        eventName: 'refund',
      }
    )
  }
}
