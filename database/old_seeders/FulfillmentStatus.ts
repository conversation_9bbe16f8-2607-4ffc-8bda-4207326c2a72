import BaseSeeder from '@ioc:Adonis/Lucid/Seeder'
import FulfillmentStatus from 'App/Models/FulfillmentStatus'

export default class FulfillmentStatusSeeder extends BaseSeeder {
  public async run() {
    // Write your database queries inside the run method
    // await FulfillmentStatus.create({
    //   status: 'pending',
    // })

    // await FulfillmentStatus.create({
    //   status: 'out of stock',
    // })

    // await FulfillmentStatus.create({
    //   status: 'order packaging',
    // })

    // await FulfillmentStatus.create({
    //   status: 'waiting to be shipped',
    // })

    // await FulfillmentStatus.create({
    //   status: 'shipped',
    // })

    // await FulfillmentStatus.create({
    //   status: 'cancelled',
    // })
    await FulfillmentStatus.updateOrCreate(
      {
        id: 1,
      },
      {
        status: 'pending',
      }
    )

    await FulfillmentStatus.updateOrCreate(
      { id: 2 },
      {
        status: 'success',
      }
    )

    await FulfillmentStatus.updateOrCreate(
      { id: 3 },
      {
        status: 'failed',
      }
    )

    await FulfillmentStatus.updateOrCreate(
      { id: 4 },
      {
        status: 'cancelled',
      }
    )

    await FulfillmentStatus.updateOrCreate(
      { id: 5 },
      {
        status: 'error',
      }
    )

    // const status6 = await FulfillmentStatus.find(6)
    // await status6?.delete()
  }
}
