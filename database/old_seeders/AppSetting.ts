import FulfillmentSetting from 'App/Models/FulfillmentSetting'
import BaseSeeder from '@ioc:Adonis/Lucid/Seeder'
import AppSetting from 'App/Models/AppSetting'
import YamatoSetting from 'App/Models/YamatoSetting'

export default class AppSettingSeeder extends BaseSeeder {
  public async run() {
    const fulfillmentSetting = await FulfillmentSetting.create({
      name: 'ケースフィニット株式会社',
      phone: '080-6583-9620',
      postal: '1700005',
      province: '東京都',
      city: '豊島区南大塚',
      address1: '3-24-4 MTビル 2階',
      settingName: '南大塚用',
    })

    const yamatoSettng = await YamatoSetting.create({
      customerNo: '090600551570',
      unchinNo: '01',
      kurijouType: '7',
      handling1: 'ワレ物注意',
      handling2: '下積厳禁',
      settingName: '南大塚用',
    })

    await AppSetting.create({
      fulfillmentSettingId: fulfillmentSetting.id,
      yamatoSettingId: yamatoSettng.id,
    })
    // Write your database queries inside the run method
  }
}
