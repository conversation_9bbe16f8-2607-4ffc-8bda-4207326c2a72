import AppSetting from 'App/Models/AppSetting'
import BaseSeeder from '@ioc:Adonis/Lucid/Seeder'
import ShopifySetting from 'App/Models/ShopifySetting'

export default class ShopifySettingSeeder extends BaseSeeder {
  public async run() {
    const shopifySetting = await ShopifySetting.updateOrCreate(
      { id: 1 },
      {
        outLocationId: '34650030151',
        settingName: '南大塚用',
      }
    )

    await AppSetting.query().update({
      shopify_setting_id: shopifySetting.id,
    })
  }
}
