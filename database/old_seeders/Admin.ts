import BaseSeeder from '@ioc:Adonis/Lucid/Seeder'
import User from 'App/Models/User'

export default class AdminSeeder extends BaseSeeder {
  public async run() {
    // Write your database queries inside the run method
    const user = await User.create({
      email: '<EMAIL>',
      password: 'abcd1234',
    })
    user.related('admin').create({
      type: 0,
      productsAccess: true,
      usersAccess: true,
      eventsAccess: true,
      ordersAccess: true,
      stocksAccess: true,
      returnsAccess: true,
      reviewsAccess: true,
      sourcesAccess: true,
      customersAccess: true,
      blacklistsAccess: true,
      ordersEditAccess: true,
      warehousesAccess: true,
      userPermissionsAccess: true,
    })

    const user2 = await User.create({
      email: '<EMAIL>',
      password: 'abcd1234',
    })
    user2.related('admin').create({
      type: 0,
      productsAccess: true,
      usersAccess: false,
      eventsAccess: false,
      ordersAccess: true,
      stocksAccess: false,
      returnsAccess: false,
      reviewsAccess: false,
      sourcesAccess: false,
      customersAccess: false,
      blacklistsAccess: false,
      ordersEditAccess: false,
      warehousesAccess: false,
      userPermissionsAccess: false,
    })

    const user3 = await User.create({
      email: '<EMAIL>',
      password: 'abcd1234',
    })
    user3.related('admin').create({
      type: 0,
      productsAccess: true,
      usersAccess: false,
      eventsAccess: false,
      ordersAccess: true,
      stocksAccess: false,
      returnsAccess: false,
      reviewsAccess: false,
      sourcesAccess: false,
      customersAccess: false,
      blacklistsAccess: false,
      ordersEditAccess: false,
      warehousesAccess: false,
      userPermissionsAccess: false,
    })
  }
}
