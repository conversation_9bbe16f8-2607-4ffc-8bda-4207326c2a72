import BaseSeeder from '@ioc:Adonis/Lucid/Seeder'
import StockQuality from 'App/Models/StockQuality'

export default class StockQualitySeeder extends BaseSeeder {
  public async run() {
    // Write your database queries inside the run method
    await StockQuality.create({
      name: 'brand new',
    })

    await StockQuality.create({
      name: 'defected',
    })

    await StockQuality.create({
      name: 'return',
    })
  }
}
