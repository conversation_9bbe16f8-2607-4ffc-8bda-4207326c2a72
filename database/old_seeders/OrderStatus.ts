import BaseSeeder from '@ioc:Adonis/Lucid/Seeder'
import OrderStatus from 'App/Models/OrderStatus'

export default class OrderStatusSeeder extends BaseSeeder {
  public async run() {
    // Write your database queries inside the run method
    await OrderStatus.updateOrCreate(
      { id: 1 },
      {
        status: 'pending',
      }
    )

    await OrderStatus.updateOrCreate(
      { id: 2 },
      {
        status: 'out of stock',
      }
    )

    await OrderStatus.updateOrCreate(
      { id: 3 },
      {
        status: 'order packaging',
      }
    )

    await OrderStatus.updateOrCreate(
      { id: 4 },
      {
        status: 'waiting to be shipped',
      }
    )

    await OrderStatus.updateOrCreate(
      { id: 5 },
      {
        status: 'shipped',
      }
    )

    await OrderStatus.updateOrCreate(
      { id: 6 },
      {
        status: 'partially shipped',
      }
    )

    await OrderStatus.updateOrCreate(
      { id: 7 },
      {
        status: 'cancelled',
      }
    )

    await OrderStatus.updateOrCreate(
      { id: 8 },
      {
        status: 'refuded',
      }
    )
  }
}
