import BaseSeeder from '@ioc:Adonis/Lucid/Seeder'
import Status from 'App/Models/Status'

export default class StatusSeeder extends BaseSeeder {
  public async run() {
    // Write your database queries inside the run method
    await Status.create({
      statusName: 'successful',
    })

    await Status.create({
      statusName: 'pending',
    })

    await Status.create({
      statusName: 'failed',
    })
  }
}
