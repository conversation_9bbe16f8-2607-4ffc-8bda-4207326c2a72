import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class Orders extends BaseSchema {
  protected tableName = 'orders'

  public async up() {
    this.schema.createTable(this.tableName, (table) => {
      table.increments('id')
      table.string('order')
      table
        .integer('customer_id')
        .unsigned()
        .references('id')
        .inTable('customers')
        .onDelete('SET NULL')
      table.string('zip')
      table.string('address')
      table.string('apartment')
      table.string('company')
      table.string('tracking')
      table
        .integer('fulfillment_status_id')
        .unsigned()
        .references('id')
        .inTable('fulfillment_statuses')
        .onDelete('SET NULL')
      table
        .integer('transaction_status_id')
        .unsigned()
        .references('id')
        .inTable('transaction_statuses')
        .onDelete('SET NULL')
      table.boolean('generated').defaultTo('false')
      /**
       * Uses timestamptz for PostgreSQL and DATETIME2 for MSSQL
       */
      table.timestamps(true, true)
    })
  }

  public async down() {
    this.schema.dropTable(this.tableName)
  }
}
