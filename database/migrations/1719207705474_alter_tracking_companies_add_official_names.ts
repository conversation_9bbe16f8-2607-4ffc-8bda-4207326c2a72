import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class TrackingCompanies extends BaseSchema {
  protected tableName = 'tracking_companies'

  public async up() {
    this.schema.alterTable(this.tableName, (table) => {
      table.string('official_name')
    })
  }

  public async down() {
    this.schema.alterTable(this.tableName, (table) => {
      table.dropColumn('official_name')
    })
  }
}
