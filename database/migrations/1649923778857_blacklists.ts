import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class Blacklists extends BaseSchema {
  protected tableName = 'blacklists'

  public async up() {
    this.schema.createTable(this.tableName, (table) => {
      table.increments('id')
      table
        .integer('customer_id')
        .unsigned()
        .references('id')
        .inTable('customers')
        .onDelete('SET NULL')
      table.string('remarks')
      /**
       * Uses timestamptz for PostgreSQL and DATETIME2 for MSSQL
       */
      table.timestamps(true, true)
    })
  }

  public async down() {
    this.schema.dropTable(this.tableName)
  }
}
