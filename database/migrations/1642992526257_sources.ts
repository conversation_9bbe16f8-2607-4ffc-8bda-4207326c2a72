import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class Sources extends BaseSchema {
  protected tableName = 'sources'

  public async up() {
    this.schema.createTable(this.tableName, (table) => {
      table.increments('id').primary()
      table.string('source_name').notNullable()
      table.boolean('was_deleted').defaultTo(false)

      /**
       * Uses timestamptz for PostgreSQL and DATETIME2 for MSSQL
       */
      table.timestamps(true, true)
    })
  }

  public async down() {
    this.schema.dropTable(this.tableName)
  }
}
