import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class Orders extends BaseSchema {
  protected tableName = 'orders'

  public async up() {
    this.schema.alterTable(this.tableName, (table) => {
      table.dropForeign('tracking_company_id')
      table.dropColumn('tracking_company_id')
      table.string('tracking_company_name').nullable()
      table.string('tracking_url').nullable()
    })
  }

  public async down() {
    this.schema.alterTable(this.tableName, (table) => {
      table
        .integer('tracking_company_id')
        .unsigned()
        .references('id')
        .inTable('tracking_companies')
        .nullable()

      table.dropColumn('tracking_company_name')
      table.dropColumn('tracking_url')
    })
  }
}
