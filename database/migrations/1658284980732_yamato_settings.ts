import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class YamatoSettings extends BaseSchema {
  protected tableName = 'yamato_settings'

  public async up() {
    this.schema.createTable(this.tableName, (table) => {
      table.increments('id')
      table.text('customer_no').notNullable()
      table.text('unchin_no').nullable()

      /**
       * Uses timestamptz for PostgreSQL and DATETIME2 for MSSQL
       */
      table.timestamp('created_at', { useTz: true })
      table.timestamp('updated_at', { useTz: true })
    })
  }

  public async down() {
    this.schema.dropTable(this.tableName)
  }
}
