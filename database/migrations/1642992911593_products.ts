import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class Products extends BaseSchema {
  protected tableName = 'products'

  public async up() {
    this.schema.createTable(this.tableName, (table) => {
      table.increments('id').primary()
      table.string('product_name').notNullable()
      table.text('short_description').notNullable()
      table.boolean('shopify_sync').defaultTo(true).notNullable()
      table.boolean('amazon_sync').defaultTo(true).notNullable()
      table.string('sku').notNullable().unique()
      table.string('shopify_id').defaultTo('').nullable()
      table.string('option_1').defaultTo('').nullable()
      table.string('option_2').defaultTo('').nullable()
      table.string('option_3').defaultTo('').nullable()

      /**
       * Uses timestamptz for PostgreSQL and DATETIME2 for MSSQL
       */
      table.timestamps(true, true)
    })
  }

  public async down() {
    this.schema.dropTable(this.tableName)
  }
}
