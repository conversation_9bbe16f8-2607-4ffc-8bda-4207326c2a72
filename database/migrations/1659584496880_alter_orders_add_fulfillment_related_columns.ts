import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class Orders extends BaseSchema {
  protected tableName = 'orders'

  public async up() {
    this.schema.alterTable(this.tableName, (table) => {
      table
        .integer('tracking_company_id')
        .unsigned()
        .references('id')
        .inTable('tracking_companies')
        .nullable()

      table
        .integer('shop_platform_id')
        .unsigned()
        .references('id')
        .inTable('shop_platforms')
        .nullable()

      table.timestamp('fulfilled_at').nullable()
    })
  }

  public async down() {
    this.schema.alterTable(this.tableName, (table) => {
      table.dropForeign('tracking_company_id')
      table.dropForeign('shop_platform_id')
      table.dropColumn('tracking_company_id')
      table.dropColumn('shop_platform_id')
      table.dropColumn('fulfilled_at')
    })
  }
}
