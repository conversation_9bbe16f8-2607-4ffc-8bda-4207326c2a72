import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class extends BaseSchema {
  protected tableName = 'weight_based_shipping_rules'

  public async up() {
    this.schema.createTable(this.tableName, (table) => {
      table.increments('id').primary()
      table.integer('max_weight').notNullable().comment('Maximum weight in grams for this rule')
      table.integer('tracking_company_id').unsigned().notNullable()
        .references('id').inTable('tracking_companies').onDelete('CASCADE')
      table.integer('priority').notNullable().defaultTo(1).comment('Rule priority (lower = higher priority)')
      table.timestamps(true)

      // Add index for efficient querying
      table.index(['max_weight', 'priority'])
    })
  }

  public async down() {
    this.schema.dropTable(this.tableName)
  }
}