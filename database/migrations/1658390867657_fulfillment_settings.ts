import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class FulfillmentSettings extends BaseSchema {
  protected tableName = 'fulfillment_settings'

  public async up() {
    this.schema.createTable(this.tableName, (table) => {
      table.increments('id')
      table.string('name')
      table.string('phone')
      table.string('postal')
      table.string('province')
      table.string('city')
      table.string('address_1')
      table.string('address_2').nullable()

      /**
       * Uses timestamptz for PostgreSQL and DATETIME2 for MSSQL
       */
      table.timestamp('created_at', { useTz: true })
      table.timestamp('updated_at', { useTz: true })
    })
  }

  public async down() {
    this.schema.dropTable(this.tableName)
  }
}
