import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class AppSettings extends BaseSchema {
  protected tableName = 'app_settings'

  public async up() {
    this.schema.alterTable(this.tableName, (table) => {
      table
        .integer('shopify_setting_id')
        .unsigned()
        .references('id')
        .inTable('shopify_settings')
        .onDelete('SET NULL')
        .nullable()
    })
  }

  public async down() {
    this.schema.alterTable(this.tableName, (table) => {
      table.dropForeign('shopify_setting_id')
      table.dropColumn('shopify_setting_id')
    })
  }
}
