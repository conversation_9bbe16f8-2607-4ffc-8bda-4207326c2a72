import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class Stocks extends BaseSchema {
  protected tableName = 'stocks'

  public async up() {
    this.schema.alterTable(this.tableName, (table) => {
      table
        .integer('variant_id')
        .unsigned()
        .references('id')
        .inTable('product_variants')
        .onDelete('SET NULL')
    })
  }

  public async down() {
    this.schema.alterTable(this.tableName, (table) => {
      table.dropForeign('variant_id')
    })
  }
}
