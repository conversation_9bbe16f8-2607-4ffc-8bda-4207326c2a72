import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class LineItems extends BaseSchema {
  protected tableName = 'line_items'

  public async up() {
    this.schema.alterTable(this.tableName, (table) => {
      table.unique(['order_id', 'sku'])
    })
  }

  public async down() {
    // this.schema.alterTable(this.tableName, (table) => {
    //   table.dropForeign('order_id')
    //   table.index('order_id_sku_unique')
    // })
  }
}
