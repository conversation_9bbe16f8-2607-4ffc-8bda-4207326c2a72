import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class YahooTokens extends BaseSchema {
  protected tableName = 'yahoo_tokens'

  public async up() {
    this.schema.createTable(this.tableName, (table) => {
      table.increments('id')
      table.text('access_token').nullable()
      table.text('refresh_token').nullable()
      table.string('token_type').nullable()

      /**
       * Uses timestamptz for PostgreSQL and DATETIME2 for MSSQL
       */
      table.timestamp('created_at', { useTz: true })
      table.timestamp('updated_at', { useTz: true })
    })
  }

  public async down() {
    this.schema.dropTable(this.tableName)
  }
}
