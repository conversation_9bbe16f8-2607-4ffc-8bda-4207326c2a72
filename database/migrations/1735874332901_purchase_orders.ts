import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class PurchaseOrders extends BaseSchema {
  protected tableName = 'purchase_orders'

  public async up() {
    this.schema.createTable(this.tableName, (table) => {
      table.increments('id')
      table.string('receiver_email').notNullable()
      table.text('remark', 'longtext').nullable()
      table.string('status').nullable()

      /**
       * Uses timestamptz for PostgreSQL and DATETIME2 for MSSQL
       */
      table.timestamp('created_at', { useTz: true })
      table.timestamp('updated_at', { useTz: true })
    })
  }

  public async down() {
    this.schema.dropTable(this.tableName)
  }
}
