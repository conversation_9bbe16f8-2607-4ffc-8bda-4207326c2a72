import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class FulfillmentStatuses extends BaseSchema {
  protected tableName = 'fulfillment_statuses'

  public async up() {
    this.schema.createTable(this.tableName, (table) => {
      table.increments('id')
      table.string('status')
      /**
       * Uses timestamptz for PostgreSQL and DATETIME2 for MSSQL
       */
      table.timestamps(true, true)
    })
  }

  public async down() {
    this.schema.dropTable(this.tableName)
  }
}
