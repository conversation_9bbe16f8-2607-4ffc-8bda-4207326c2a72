import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class ProductVariants extends BaseSchema {
  protected tableName = 'product_variants'

  public async up() {
    this.schema.alterTable(this.tableName, (table) => {
      table.boolean('availability').defaultTo(true).notNullable()
    })
  }

  public async down() {
    this.schema.alterTable(this.tableName, (table) => {
      table.dropColumn('availability')
    })
  }
}
