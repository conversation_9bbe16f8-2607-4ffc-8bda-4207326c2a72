import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class Admins extends BaseSchema {
  protected tableName = 'admins'

  public async up() {
    this.schema.alterTable(this.tableName, (table) => {
      table.boolean('products_access').defaultTo(false).notNullable()
      table.boolean('reviews_access').defaultTo(false).notNullable()
      table.boolean('user_permissions_access').defaultTo(false).notNullable()
      table.boolean('users_access').defaultTo(false).notNullable()
      table.boolean('stocks_access').defaultTo(false).notNullable()
      table.boolean('events_access').defaultTo(false).notNullable()
      table.boolean('warehouses_access').defaultTo(false).notNullable()
      table.boolean('returns_access').defaultTo(false).notNullable()
      table.boolean('sources_access').defaultTo(false).notNullable()
      table.boolean('orders_access').defaultTo(false).notNullable()
      table.boolean('orders_edit_access').defaultTo(false).notNullable()
    })
  }

  public async down() {
    this.schema.alterTable(this.tableName, (table) => {
      table.dropColumns(
        'products_access',
        'reviews_access',
        'user_permissions_access',
        'users_access',
        'stocks_access',
        'events_access',
        'warehouses_access',
        'returns_access',
        'sources_access',
        'orders_access',
        'orders_edit_access'
      )
    })
  }
}
