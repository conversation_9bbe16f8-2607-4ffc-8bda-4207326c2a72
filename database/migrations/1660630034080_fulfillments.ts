import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class Fulfillments extends BaseSchema {
  protected tableName = 'fulfillments'

  public async up() {
    this.schema.createTable(this.tableName, (table) => {
      table.increments('id')
      table.string('name')
      table.boolean('notify_user').defaultTo(0)
      table.integer('order_id').unsigned().references('id').inTable('orders').onDelete('SET NULL')
      table.json('receipt')
      table.string('service')
      // table.string('shipment_status')
      // table.string('status')
      table
        .integer('fulfillment_status_id')
        .unsigned()
        .references('id')
        .inTable('fulfillment_statuses')
        .onDelete('SET NULL')
      table.json('tracking_info')
      table.string('tracking_company_name')
      table.string('tracking_number')
      table.string('tracking_urls')
      table.string('tracking_url')
      // table.string('note')
      table.timestamps(true, true)
    })
  }

  public async down() {
    this.schema.dropTable(this.tableName)
  }
}
