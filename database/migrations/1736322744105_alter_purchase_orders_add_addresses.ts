import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class PurchaseOrders extends BaseSchema {
  protected tableName = 'purchase_orders'

  public async up() {
    this.schema.alterTable(this.tableName, (table) => {
      table.string('first_name').nullable()
      table.string('last_name').nullable()
      table.string('phone').nullable()
      table.string('zip').nullable()
      table.string('address').nullable()
      table.string('apartment').nullable()
      table.string('company').nullable()
    })
  }

  public async down() {
    this.schema.alterTable(this.tableName, (table) => {
      table.dropColumn('first_name')
      table.dropColumn('last_name')
      table.dropColumn('phone')
      table.dropColumn('zip')
      table.dropColumn('address')
      table.dropColumn('apartment')
      table.dropColumn('company')
    })
  }
}
