import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class Casefinites extends BaseSchema {
  protected tableName = 'casefinites'

  public async up() {
    this.schema.createTable(this.tableName, (table) => {
      table.increments('id')
      table.string('name')
      table.string('phone')
      table.string('postal')
      table.string('address')

      /**
       * Uses timestamptz for PostgreSQL and DATETIME2 for MSSQL
       */
      table.timestamps(true, true)
    })
  }

  public async down() {
    this.schema.dropTable(this.tableName)
  }
}
