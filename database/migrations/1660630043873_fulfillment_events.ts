import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class FulfillmentEvents extends BaseSchema {
  protected tableName = 'fulfillment_events'

  public async up() {
    this.schema.createTable(this.tableName, (table) => {
      table.increments('id')
      table.string('address_1')
      table.string('city')
      table.string('province')
      table.string('country')
      table.string('zip')
      table.integer('order_id').unsigned().references('id').inTable('orders').onDelete('SET NULL')
      table
        .integer('fulfillment_id')
        .unsigned()
        .references('id')
        .inTable('fulfillments')
        .onDelete('SET NULL')
      table.dateTime('happened_at')
      table.dateTime('estimated_delivery_at')
      table.string('latitude')
      table.string('longitude')
      table.string('message')
      table.string('status')
      table.timestamps(true, true)
    })
  }

  public async down() {
    this.schema.dropTable(this.tableName)
  }
}
