import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class Sources extends BaseSchema {
  protected tableName = 'sources'

  public async up() {
    this.schema.alterTable(this.tableName, (table) => {
      table.string('secret_key').nullable()
      table.string('api_key').nullable()
    })
  }

  public async down() {
    this.schema.alterTable(this.tableName, (table) => {
      table.dropColumn('secret_key')
      table.dropColumn('api_key')
    })
  }
}
