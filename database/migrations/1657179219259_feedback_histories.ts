import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class FeedbackHistories extends BaseSchema {
  protected tableName = 'feedback_histories'

  public async up() {
    this.schema.createTable(this.tableName, (table) => {
      table.increments('id')
      table
        .integer('feedback_id')
        .unsigned()
        .references('id')
        .inTable('feedbacks')
        .onDelete('SET NULL')
      table
        .integer('admin_id')
        .nullable()
        .unsigned()
        .references('id')
        .inTable('admins')
        .onDelete('SET NULL')
      table.string('content').notNullable()
      /**
       * Uses timestamptz for PostgreSQL and DATETIME2 for MSSQL
       */
      table.timestamp('created_at', { useTz: true })
      table.timestamp('updated_at', { useTz: true })
    })
  }

  public async down() {
    this.schema.dropTable(this.tableName)
  }
}
