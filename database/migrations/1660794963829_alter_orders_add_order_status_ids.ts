import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class Orders extends BaseSchema {
  protected tableName = 'orders'

  public async up() {
    this.schema.alterTable(this.tableName, (table) => {
      table
        .integer('order_status_id')
        .unsigned()
        .references('id')
        .inTable('order_statuses')
        .onDelete('SET NULL')
    })
  }

  public async down() {
    this.schema.alterTable(this.tableName, (table) => {
      table.dropForeign('order_status_id')
      table.dropColumn('order_status_id')
    })
  }
}
