import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class Blacklists extends BaseSchema {
  protected tableName = 'blacklists'

  public async up() {
    this.schema.alterTable(this.tableName, (table) => {
      table.string('email').nullable().defaultTo('')
      table.string('first_name').nullable().defaultTo('')
      table.string('last_name').nullable().defaultTo('')
      table.string('address').nullable().defaultTo('')
      table.string('appartment').nullable().defaultTo('')
      table.string('company').nullable().defaultTo('')
      table.text('remark').nullable().defaultTo('')
    })
  }

  public async down() {
    this.schema.alterTable(this.tableName, (table) => {
      table.dropColumn('email')
      table.dropColumn('first_name')
      table.dropColumn('last_name')
      table.dropColumn('address')
      table.dropColumn('appartment')
      table.dropColumn('company')
      table.dropColumn('remark')
    })
  }
}
