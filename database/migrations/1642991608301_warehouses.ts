import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class Warehouses extends BaseSchema {
  protected tableName = 'warehouses'

  public async up() {
    this.schema.createTable(this.tableName, (table) => {
      table.increments('id').primary()
      table.string('warehouse_name').notNullable()
      table.string('location').notNullable()
      table.string('address').notNullable()
      table.boolean('is_default').defaultTo(false)

      /**
       * Uses timestamptz for PostgreSQL and DATETIME2 for MSSQL
       */
      table.timestamps(true, true)
    })
  }

  public async down() {
    this.schema.dropTable(this.tableName)
  }
}
