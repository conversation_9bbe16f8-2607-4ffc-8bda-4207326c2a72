import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class YamatoSettings extends BaseSchema {
  protected tableName = 'yamato_settings'

  public async up() {
    this.schema.alterTable(this.tableName, (table) => {
      table.string('setting_name')
    })
  }

  public async down() {
    this.schema.alterTable(this.tableName, (table) => {
      table.dropColumn('setting_name')
    })
  }
}
