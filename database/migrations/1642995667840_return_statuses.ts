import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class ReturnStatuses extends BaseSchema {
  protected tableName = 'return_statuses'

  public async up() {
    this.schema.createTable(this.tableName, (table) => {
      table.increments('id').primary()
      table.integer('event_id').unsigned().references('id').inTable('events').onDelete('SET NULL')
      table.boolean('is_defect').defaultTo(false)
      table.string('return_reason').nullable()

      /**
       * Uses timestamptz for PostgreSQL and DATETIME2 for MSSQL
       */
      table.timestamps(true, true)
    })
  }

  public async down() {
    this.schema.dropTable(this.tableName)
  }
}
