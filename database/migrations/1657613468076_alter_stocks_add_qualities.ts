import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class Stocks extends BaseSchema {
  protected tableName = 'stocks'

  public async up() {
    this.schema.alterTable(this.tableName, (table) => {
      table
        .integer('quality_id')
        .unsigned()
        .references('id')
        .inTable('stock_qualities')
        .defaultTo(1)
        .onDelete('SET NULL')
    })
  }

  public async down() {
    this.schema.alterTable(this.tableName, (table) => {
      table.dropForeign('quality_id')
    })
  }
}
