import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class Feedbacks extends BaseSchema {
  protected tableName = 'feedbacks'

  public async up() {
    this.schema.alterTable(this.tableName, (table) => {
      table.boolean('is_closed').notNullable().defaultTo(false)
      table
        .integer('feedback_platform_id')
        .unsigned()
        .references('id')
        .inTable('feedback_platforms')
        .onDelete('SET NULL')
    })
  }

  public async down() {
    this.schema.alterTable(this.tableName, (table) => {
      table.dropColumn('is_closed')
      table.dropForeign('feedback_platform_id')
      table.dropColumn('feedback_platform_id')
    })
  }
}
