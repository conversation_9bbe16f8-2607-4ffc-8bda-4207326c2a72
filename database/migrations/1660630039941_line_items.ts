import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class LineItems extends BaseSchema {
  protected tableName = 'line_items'

  public async up() {
    this.schema.createTable(this.tableName, (table) => {
      table.increments('id')
      table.string('key')
      table.string('platform_line_item_id')
      // table.string('title').notNullable()
      table.decimal('price')
      table.decimal('line_price')
      table.decimal('final_price')
      table.decimal('final_line_price')
      table.integer('quantity').unsigned()
      table.string('sku')
      table.integer('grams').unsigned()
      table.string('vendor')
      table.boolean('taxable').defaultTo(false)
      table.json('properties')
      table.json('featured_image')
      table.string('image_url')
      table.string('handle')
      table.boolean('requires_shipping').defaultTo(false)
      table.boolean('gift_card').defaultTo(false)
      // table.string('url')
      table.string('name')
      table.string('product_title')
      table.string('product_description')
      table.string('product_type')
      table.string('variant_title')
      table.string('variant_option')
      table.json('options_with_values')
      table.integer('fulfillable_quantity').unsigned()
      table.string('fulfillment_service')
      table.string('fulfillment_status')
      table.json('tax_lines')
      table.decimal('price_set')
      table.decimal('total_discount')
      table.json('total_discount_set')
      table.json('discount_allocations')
      table.json('duties')
      table.boolean('product_has_only_default_variant').defaultTo(false)
      table.integer('order_id').unsigned().references('id').inTable('orders').onDelete('SET NULL')
      table
        .integer('fulfillment_id')
        .unsigned()
        .references('id')
        .inTable('fulfillments')
        .onDelete('SET NULL')
      // table
      //   .integer('product_id')
      //   .unsigned()
      //   .references('id')
      //   .inTable('products')
      //   .onDelete('SET NULL')
      // table
      //   .integer('variant_id')
      //   .unsigned()
      //   .references('id')
      //   .inTable('product_variants')
      //   .onDelete('SET NULL')
      table.integer('event_id').unsigned().references('id').inTable('events').onDelete('SET NULL')
      table.timestamps(true, true)
    })
  }

  public async down() {
    this.schema.dropTable(this.tableName)
  }
}
