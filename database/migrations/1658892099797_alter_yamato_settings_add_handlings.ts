import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class YamatoSettings extends BaseSchema {
  protected tableName = 'yamato_settings'

  public async up() {
    this.schema.alterTable(this.tableName, (table) => {
      table.string('handling_1').defaultTo('')
      table.string('handling_2').defaultTo('')
    })
  }

  public async down() {
    this.schema.alterTable(this.tableName, (table) => {
      table.dropColumn('handling_1')
      table.dropColumn('handling_2')
    })
  }
}
