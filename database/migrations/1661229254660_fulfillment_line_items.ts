import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class FulfillmentLineItems extends BaseSchema {
  protected tableName = 'fulfillment_line_items'

  public async up() {
    this.schema.createTable(this.tableName, (table) => {
      table.increments('id')
      table
        .integer('line_item_id')
        .unsigned()
        .references('id')
        .inTable('line_items')
        .onDelete('SET NULL')

      table
        .integer('fulfillment_id')
        .unsigned()
        .references('id')
        .inTable('fulfillments')
        .onDelete('SET NULL')

      table.integer('event_id').unsigned().references('id').inTable('events').onDelete('SET NULL')

      /**
       * Uses timestamptz for PostgreSQL and DATETIME2 for MSSQL
       */
      table.timestamp('created_at', { useTz: true })
      table.timestamp('updated_at', { useTz: true })
    })
  }

  public async down() {
    this.schema.dropTable(this.tableName)
  }
}
