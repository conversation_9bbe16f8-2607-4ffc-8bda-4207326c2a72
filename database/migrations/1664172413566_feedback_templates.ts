import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class FeedbackTemplates extends BaseSchema {
  protected tableName = 'feedback_templates'

  public async up() {
    this.schema.createTable(this.tableName, (table) => {
      table.increments('id')
      table.json('optional_properties').nullable()
      table.string('title').notNullable()
      table.text('content', 'longtext').notNullable()

      /**
       * Uses timestamptz for PostgreSQL and DATETIME2 for MSSQL
       */
      table.timestamp('created_at', { useTz: true })
      table.timestamp('updated_at', { useTz: true })
    })
  }

  public async down() {
    this.schema.dropTable(this.tableName)
  }
}
