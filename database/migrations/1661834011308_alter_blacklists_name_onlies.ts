import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class Blacklists extends BaseSchema {
  protected tableName = 'blacklists'

  public async up() {
    this.schema.alterTable(this.tableName, (table) => {
      table.dropColumn('first_name')
      table.dropColumn('last_name')
      table.string('name').nullable().defaultTo('')
    })
  }

  public async down() {
    this.schema.alterTable(this.tableName, (table) => {
      table.string('first_name').nullable().defaultTo('')
      table.string('last_name').nullable().defaultTo('')
      table.dropColumn('name')
    })
  }
}
