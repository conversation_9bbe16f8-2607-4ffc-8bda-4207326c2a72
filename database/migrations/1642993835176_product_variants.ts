import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class ProductVariants extends BaseSchema {
  protected tableName = 'product_variants'

  public async up() {
    this.schema.createTable(this.tableName, (table) => {
      table.increments('id').primary()
      table.string('variant_name').notNullable()
      table.string('sku').notNullable().unique()
      table.string('amazon_sku').defaultTo(null).unique()
      table.string('shopify_sku').defaultTo(null).unique()
      table
        .integer('product_id')
        .unsigned()
        .references('id')
        .inTable('products')
        .onDelete('SET NULL')
      table.integer('price').defaultTo(0)
      table.string('barcode').defaultTo(null)
      table.integer('weight').defaultTo(0)
      table.string('option_1').notNullable()
      table.string('option_2').defaultTo('').nullable()
      table.string('option_3').defaultTo('').nullable()
      table.string('shopify_id').defaultTo(null)
      table.string('shopify_stock_id').defaultTo(null).nullable()
      table.string('shopify_location_id').defaultTo(null).nullable()
      /**
       * Uses timestamptz for PostgreSQL and DATETIME2 for MSSQL
       */
      table.timestamps(true, true)
    })
  }

  public async down() {
    this.schema.dropTable(this.tableName)
  }
}
