import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class AppSettings extends BaseSchema {
  protected tableName = 'app_settings'

  public async up() {
    this.schema.createTable(this.tableName, (table) => {
      table.increments('id')
      table
        .integer('fulfillment_setting_id')
        .unsigned()
        .references('id')
        .inTable('fulfillment_settings')
        .onDelete('SET NULL')
        .nullable()
      table
        .integer('yamato_setting_id')
        .unsigned()
        .references('id')
        .inTable('yamato_settings')
        .onDelete('SET NULL')
        .nullable()
      /**
       * Uses timestamptz for PostgreSQL and DATETIME2 for MSSQL
       */
      table.timestamp('created_at', { useTz: true })
      table.timestamp('updated_at', { useTz: true })
    })
  }

  public async down() {
    this.schema.dropTable(this.tableName)
  }
}
