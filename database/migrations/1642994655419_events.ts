import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class Events extends BaseSchema {
  protected tableName = 'events'

  public async up() {
    this.schema.createTable(this.tableName, (table) => {
      table.increments('id').primary()
      table.integer('stock_id').unsigned().references('id').inTable('stocks').onDelete('SET NULL')
      table.integer('quantity').notNullable()
      table
        .integer('event_type_id')
        .unsigned()
        .references('id')
        .inTable('event_types')
        .onDelete('SET NULL')
      table
        .integer('status_id')
        .unsigned()
        .references('id')
        .inTable('statuses')
        .onDelete('SET NULL')
      table.integer('source_id').unsigned().references('id').inTable('sources').onDelete('SET NULL')
      table.string('remark').nullable()

      /**
       * Uses timestamptz for PostgreSQL and DATETIME2 for MSSQL
       */
      table.timestamps(true, true)
    })
  }

  public async down() {
    this.schema.dropTable(this.tableName)
  }
}
