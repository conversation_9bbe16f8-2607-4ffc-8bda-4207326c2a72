import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class Orders extends BaseSchema {
  protected tableName = 'orders'

  public async up() {
    this.schema.alterTable(this.tableName, (table) => {
      table.dateTime('delivery_mail_sent_at').nullable()
      table.dateTime('confirmation_mail_sent_at').nullable()
    })
  }

  public async down() {
    this.schema.alterTable(this.tableName, (table) => {
      table.dropColumn('delivery_mail_sent_at')
      table.dropColumn('confirmation_mail_sent_at')
    })
  }
}
