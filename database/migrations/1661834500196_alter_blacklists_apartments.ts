import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class Blacklists extends BaseSchema {
  protected tableName = 'blacklists'

  public async up() {
    this.schema.alterTable(this.tableName, (table) => {
      table.renameColumn('appartment', 'apartment')
    })
  }

  public async down() {
    this.schema.alterTable(this.tableName, (table) => {
      table.renameColumn('apartment', 'appartment')
    })
  }
}
