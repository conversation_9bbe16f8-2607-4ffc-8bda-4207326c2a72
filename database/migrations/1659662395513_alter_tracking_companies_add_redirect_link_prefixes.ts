import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class TrackingCompanies extends BaseSchema {
  protected tableName = 'tracking_companies'

  public async up() {
    this.schema.alterTable(this.tableName, (table) => {
      table.string('redirect_link_prefix').nullable()
    })
  }

  public async down() {
    this.schema.alterTable(this.tableName, (table) => {
      table.dropColumn('redirect_link_prefix')
    })
  }
}
