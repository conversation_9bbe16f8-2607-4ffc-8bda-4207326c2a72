import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class FulfillmentLineItems extends BaseSchema {
  protected tableName = 'fulfillment_line_items'

  public async up() {
    this.schema.alterTable(this.tableName, (table) => {
      table.unique(['fulfillment_id', 'line_item_id'])
    })
  }

  public async down() {
    this.schema.alterTable(this.tableName, (table) => {
      table.dropForeign('fulfillment_id')
      table.dropForeign('line_item_id')
      table.dropUnique(['fulfillment_line_event'])

      table
        .integer('fulfillment_id')
        .unsigned()
        .references('id')
        .inTable('fulfillments')
        .onDelete('SET NULL')
        .alter()

      table
        .integer('line_item_id')
        .unsigned()
        .references('id')
        .inTable('line_items')
        .onDelete('SET NULL')
        .alter()
    })
  }
}
