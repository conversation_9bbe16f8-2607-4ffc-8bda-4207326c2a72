import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class Recruitments extends BaseSchema {
  protected tableName = 'recruitments'

  public async up() {
    this.schema.createTable(this.tableName, (table) => {
      table.increments('id')

      table.string('first_name')
      table.string('last_name')
      table.string('email')
      table.text('message')
      table.string('attachment')
      table.string('position')

      /**
       * Uses timestamptz for PostgreSQL and DATETIME2 for MSSQL
       */
      table.timestamps(true, true)
    })
  }

  public async down() {
    this.schema.dropTable(this.tableName)
  }
}
