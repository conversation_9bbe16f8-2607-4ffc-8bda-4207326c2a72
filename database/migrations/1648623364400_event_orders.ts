import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class EventOrders extends BaseSchema {
  protected tableName = 'event_orders'

  public async up() {
    this.schema.createTable(this.tableName, (table) => {
      table.increments('id')
      table.integer('event_id').unsigned().references('events.id').onDelete('SET NULL')
      table.integer('order_id').unsigned().references('orders.id').onDelete('SET NULL')
      table.unique(['event_id', 'order_id'])
      /**
       * Uses timestamptz for PostgreSQL and DATETIME2 for MSSQL
       */
      table.timestamps(true, true)
    })
  }

  public async down() {
    this.schema.dropTable(this.tableName)
  }
}
