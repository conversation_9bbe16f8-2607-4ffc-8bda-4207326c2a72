import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class FulfillmentSettings extends BaseSchema {
  protected tableName = 'fulfillment_settings'

  public async up() {
    this.schema.alterTable(this.tableName, (table) => {
      table.string('default_item_name').nullable().defaultTo('')
    })
  }

  public async down() {
    this.schema.alterTable(this.tableName, (table) => {
      table.dropColumn('default_item_name')
    })
  }
}
