import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class YamatoSettings extends BaseSchema {
  protected tableName = 'yamato_settings'

  public async up() {
    this.schema.alterTable(this.tableName, (table) => {
      table.string('kurijou_type').defaultTo('0').notNullable()
    })
  }

  public async down() {
    this.schema.alterTable(this.tableName, (table) => {
      table.dropColumn('kurijou_type')
    })
  }
}
