import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class PurchaseOrderFulfillments extends BaseSchema {
  protected tableName = 'purchase_order_fulfillments'

  public async up() {
    this.schema.createTable(this.tableName, (table) => {
      table.increments('id')
      table
        .integer('purchase_order_id')
        .unsigned()
        .references('id')
        .inTable('purchase_orders')
        .onDelete('set null')
        .nullable()

      table.string('tracking_company_name')
      table.string('tracking_number')
      table.string('tracking_urls')
      table.string('tracking_url')

      table
        .integer('fulfillment_status_id')
        .unsigned()
        .references('id')
        .inTable('fulfillment_statuses')
        .onDelete('SET NULL')
      /**
       * Uses timestamptz for PostgreSQL and DATETIME2 for MSSQL
       */
      table.timestamp('created_at', { useTz: true })
      table.timestamp('updated_at', { useTz: true })
    })
  }

  public async down() {
    this.schema.dropTable(this.tableName)
  }
}
