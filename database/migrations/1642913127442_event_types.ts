import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class EventTypes extends BaseSchema {
  protected tableName = 'event_types'

  public async up() {
    this.schema.createTable(this.tableName, (table) => {
      table.increments('id').primary()
      table.boolean('is_in').defaultTo(false)
      table.string('event_name').notNullable()

      /**
       * Uses timestamptz for PostgreSQL and DATETIME2 for MSSQL
       */
      table.timestamps(true, true)
    })
  }

  public async down() {
    this.schema.dropTable(this.tableName)
  }
}
