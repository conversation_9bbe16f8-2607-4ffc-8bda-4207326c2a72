import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class Reviews extends BaseSchema {
  protected tableName = 'reviews'

  public async up() {
    this.schema.alterTable(this.tableName, (table) => {
      table.string('title').defaultTo('')
      table.string('content').defaultTo('')
      table.integer('score').unsigned().notNullable()
      table.integer('votes_up').unsigned().defaultTo(0)
      table.integer('votes_down').unsigned().defaultTo(0)
      table.integer('sentimental').defaultTo(0)
      table
        .integer('product_id')
        .unsigned()
        .references('id')
        .inTable('products')
        .onDelete('SET NULL')
      table
        .integer('customer_id')
        .unsigned()
        .references('id')
        .inTable('customers')
        .onDelete('SET NULL')
      table.boolean('was_deleted').defaultTo(false)
    })
  }

  public async down() {
    this.schema.alterTable(this.tableName, (table) => {
      table.dropColumn('title')
      table.dropColumn('content')
      table.dropColumn('score')
      table.dropColumn('votes_up')
      table.dropColumn('votes_down')
      table.dropColumn('sentimental')
      table.dropForeign('customer_id')
      table.dropForeign('product_id')
      table.dropColumn('customer_id')
      table.dropColumn('product_id')
      table.dropColumn('was_deleted')
    })
  }
}
