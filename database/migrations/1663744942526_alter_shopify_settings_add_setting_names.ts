import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class ShopifySettings extends BaseSchema {
  protected tableName = 'shopify_settings'

  public async up() {
    this.schema.alterTable(this.tableName, (table) => {
      table.string('setting_name')
    })
  }

  public async down() {
    this.schema.alterTable(this.tableName, (table) => {
      table.dropColumn('setting_name')
    })
  }
}
