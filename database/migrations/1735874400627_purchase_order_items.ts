import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class PurchaseOrderItems extends BaseSchema {
  protected tableName = 'purchase_order_items'

  public async up() {
    this.schema.createTable(this.tableName, (table) => {
      table.increments('id')
      table
        .integer('purchase_order_id')
        .unsigned()
        .references('id')
        .inTable('purchase_orders')
        .onDelete('set null')
        .nullable()

      table
        .integer('variant_id')
        .unsigned()
        .references('id')
        .inTable('product_variants')
        .onDelete('set null')
        .nullable()

      table.string('sku')
      table.integer('qty').unsigned()
      /**
       * Uses timestamptz for PostgreSQL and DATETIME2 for MSSQL
       */
      table.timestamp('created_at', { useTz: true })
      table.timestamp('updated_at', { useTz: true })
    })
  }

  public async down() {
    this.schema.dropTable(this.tableName)
  }
}
