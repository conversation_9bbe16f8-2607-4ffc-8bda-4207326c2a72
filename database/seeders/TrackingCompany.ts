import BaseSeeder from '@ioc:Adonis/Lucid/Seeder'
import TrackingCompany from 'App/Models/TrackingCompany'

export default class TrackingCompanySeeder extends BaseSeeder {
  public async run() {
    await TrackingCompany.updateOrCreate(
      { id: 1 },
      {
        name: 'ヤマト運輸',
        officialName: 'ヤマト運輸',
        redirectLinkPrefix: 'https://jizen.kuronekoyamato.co.jp/jizen/servlet/crjz.b.NQ0010?id=',
      }
    )

    await TrackingCompany.updateOrCreate(
      { id: 2 },
      {
        name: '佐川急便',
        officialName: '佐川急便',
        redirectLinkPrefix: 'https://k2k.sagawa-exp.co.jp/p/web/okurijosearch.do?okurijoNo=',
      }
    )

    await TrackingCompany.updateOrCreate(
      { id: 3 },
      {
        name: '日本通運',
        officialName: '日本通運',
        redirectLinkPrefix: 'https://lp-trace.nittsu.co.jp/web/webarpaa702.srv?LANG=JP&denpyoNo1=',
      }
    )

    await TrackingCompany.updateOrCreate(
      { id: 4 },
      {
        name: '佐川ポスト投函',
        officialName: 'Japan Post (JA',
        redirectLinkPrefix: 'https://trackings.post.japanpost.jp/services/srv/search?requestNo1=',
      }
    )
  }
}
