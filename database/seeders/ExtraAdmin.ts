import BaseSeeder from '@ioc:Adonis/Lucid/Seeder'
import Admin from 'App/Models/Admin'
import User from 'App/Models/User'

export default class ExtraAdminSeeder extends BaseSeeder {
  public async run() {
    const user1 = await User.updateOrCreate(
      { email: '<EMAIL>' },
      {
        password: 'abcd1234',
      }
    )

    await Admin.updateOrCreate(
      {
        userId: user1.id,
      },
      {
        type: 0,
        productsAccess: true,
        usersAccess: false,
        eventsAccess: false,
        ordersAccess: true,
        stocksAccess: false,
        returnsAccess: false,
        reviewsAccess: false,
        sourcesAccess: false,
        customersAccess: false,
        blacklistsAccess: false,
        ordersEditAccess: false,
        warehousesAccess: false,
        userPermissionsAccess: false,
      }
    )

    const user2 = await User.updateOrCreate(
      { email: '<EMAIL>' },
      {
        password: 'BdHDuWoNL1',
      }
    )

    await Admin.updateOrCreate(
      {
        userId: user2.id,
      },
      {
        type: 0,
        productsAccess: true,
        usersAccess: false,
        eventsAccess: false,
        ordersAccess: true,
        stocksAccess: false,
        returnsAccess: false,
        reviewsAccess: false,
        sourcesAccess: false,
        customersAccess: false,
        blacklistsAccess: false,
        ordersEditAccess: false,
        warehousesAccess: false,
        userPermissionsAccess: false,
      }
    )

    const user3 = await User.create({
      email: '<EMAIL>',
      password: 'lrsv9Fc3hI',
    })
    await Admin.updateOrCreate(
      {
        userId: user3.id,
      },
      {
        type: 0,
        productsAccess: true,
        usersAccess: false,
        eventsAccess: false,
        ordersAccess: true,
        stocksAccess: false,
        returnsAccess: false,
        reviewsAccess: false,
        sourcesAccess: false,
        customersAccess: false,
        blacklistsAccess: false,
        ordersEditAccess: false,
        warehousesAccess: false,
        userPermissionsAccess: false,
      }
    )
  }
}
