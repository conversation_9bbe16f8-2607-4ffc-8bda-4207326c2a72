{"name": "casefinite-be", "version": "1.0.0", "private": true, "scripts": {"dev": "node ace serve --watch", "build": "node ace build --production", "start": "node server.js", "format": "npx prettier --write ."}, "devDependencies": {"@adonisjs/assembler": "^5.0.0", "adonis-preset-ts": "^2.1.0", "pino-pretty": "^7.0.1", "typescript": "~4.2", "youch": "^2.2.2", "youch-terminal": "^1.1.1"}, "dependencies": {"@adonisjs/auth": "^8.0.9", "@adonisjs/core": "^5.3.0", "@adonisjs/lucid": "^16.2.1", "@adonisjs/lucid-slugify": "^2.1.3", "@adonisjs/mail": "^8.1.2", "@adonisjs/repl": "^3.1.0", "@adonisjs/session": "^6.0.0", "@adonisjs/shield": "^7.0.0", "@adonisjs/view": "^6.0.0", "@google-cloud/translate": "^8.5.0", "@sendgrid/mail": "^7.7.0", "@types/archiver": "^5.3.1", "@types/lodash": "^4.14.182", "@types/qrcode": "^1.4.2", "adonis-lucid-filter": "^4.1.0", "adonis5-scheduler": "^2.0.2", "archiver": "^5.3.1", "aws-sdk": "^2.1087.0", "axios": "^0.22.0", "camelcase": "^6.2.0", "chardet": "^2.0.0", "compressing": "^1.6.2", "csv-parser": "^3.0.0", "csvtojson": "^2.0.10", "edge-js": "^16.6.0", "form-data": "^4.0.0", "iconv-lite": "^0.6.3", "jaconv": "^1.0.4", "jconv": "^0.1.5", "json-2-csv": "^3.16.0", "json2csv": "^5.0.6", "kuroshiro": "~1.1.2", "kuroshiro-analyzer-kuromoji": "^1.1.0", "lodash": "^4.17.21", "luxon": "^2.0.2", "mysql2": "^3.14.3", "node-create-csv": "^1.0.0", "node-cron": "^3.0.1", "phc-argon2": "^1.1.2", "proxy-addr": "^2.0.7", "puppeteer": "^24.10.1", "reflect-metadata": "^0.1.13", "source-map-support": "^0.5.20", "translate": "^1.4.1", "uuid": "^8.3.2", "uuidv4": "^6.2.13", "wanakana": "^4.0.2", "winrarjs": "^0.0.17"}}