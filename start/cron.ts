import View from '@ioc:Adonis/Core/View'
import sgMail, { MailDataRequired } from '@sendgrid/mail'
import { checkClosedOrders, importOrdersFromShopify } from 'App/Modules/ShopifyOrderServices'
import shopifyClient from 'App/Network/shopifyClient'
import axios, { AxiosResponse } from 'axios'
import cron from 'node-cron'

console.log('cron job started')
cron.schedule('0 3 * * *', async () => {
  if (process.env.IMPORT_CRON != '1') {
    return
  }

  var res: AxiosResponse | null = null
  var rel: string = ''
  var query = 'status=open&limit=250'
  do {
    try {
      res = await shopifyClient.get(`/orders.json?${query}`)
      rel = res?.headers['link']
        ? res?.headers['link'].split(',').find((item) => item.split('; ')[1].includes('next')) ?? ''
        : ''

      if (res?.headers['link'] && rel.includes('next')) {
        const link = res.headers['link']
          .split(',')
          .find((item) => item.split('; ')[1].includes('next'))
        query = link ? link.split('?')[1].split('>')[0] : ''
      }
      const shopifyOrders: any[] = res!.data['orders']
      if (shopifyOrders.length > 0) {
        const filteredOrder = shopifyOrders.filter((item) => {
          // const isFulfilled = item?.fulfillments?.length > 0
          const paid = item?.financial_status === 'paid'
          const authorized = item?.financial_status === 'authorized'
          // return !isFulfilled && (paid || authorized)
          return paid || authorized
        })
        await importOrdersFromShopify(filteredOrder)
      }
    } catch (e) {
      res = null
      //TODO optimize this
      console.log('cron job error while syncing orders from shopify', e)
    }
  } while (res?.headers['link'] && rel.includes('next'))
  await checkClosedOrders()
})

cron.schedule('0 */24 * * *', async () => {
  if (process.env.ABANDONED_CRON != '1') {
    return
  }

  if (process.env.SANITY_URL == null || process.env.STORE_URL == null) {
    return
  }

  const res = await shopifyClient.get('/checkouts.json')
  const checkouts: any[] = res.data['checkouts']
  checkouts.forEach(async (checkout) => {
    try {
      // TODO: setup with ENV
      // TODO: maybe store slug in local db
      const products = await Promise.all(
        await checkout.line_items.map(async (item) => {
          const sanityProduct: any = await axios.get(
            `${process.env.SANITY_URL}/v1/data/query/production?query=${encodeURIComponent(
              `*[_type == 'product' && productID == ${item.product_id}] { 'slug': slug.current }[0]`
            )}`
          )

          return {
            name: `${item.presentment_title} - ${item.presentment_variant_title}`,
            url: `${process.env.STORE_URL}/products/${sanityProduct.data?.result?.slug}`,
            quantity: item.quantity,
            total: item.price * item.quantity,
          }
        })
      )

      const htmlView = await View.render('emails/abandoned_checkout_recovery', {
        username: checkout.customer.firstName + ' ' + checkout.customer.lastName,
        email: checkout.email,
        products: products,
        currency: checkout.presentment_currency,
        checkout_url: checkout.abandoned_checkout_url,
      })
      const message: MailDataRequired = {
        to: checkout.email,
        from: process.env.MAIL_USERNAME as string,
        subject: "Don't Miss Out!",
        html: htmlView,
      }
      sgMail.setApiKey(process.env.SENDGRID_API_KEY!)
      await sgMail.send(message)
    } catch (e) {
      console.log(e)
    }
  })
})
