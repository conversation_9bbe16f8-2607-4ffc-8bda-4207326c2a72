/*
|--------------------------------------------------------------------------
| Preloaded File
|--------------------------------------------------------------------------
|
| Any code written inside this file will be executed during the application
| boot.
|
*/
import { validator } from '@ioc:Adonis/Core/Validator'

validator.rule('inValues', (value, _: any[], options) => {
  if (typeof value !== 'string') {
    return
  }

  var valid = false
  const limitValues = _[0]
  for (var limitValue of limitValues) {
    if (value === limitValue) valid = true
  }

  if (!valid) {
    options.errorReporter.report(
      options.pointer,
      'valueError',
      'value not acceptable',
      options.arrayExpressionPointer
    )
  }

  // if (
  //   value !== 'id' &&
  //   value !== 'product_name' &&
  //   value !== 'sku' &&
  //   value !== 'platform_order_id' &&
  //   value !== 'name' &&
  //   value !== 'created_at'
  // ) {
  //   options.errorReporter.report(
  //     options.pointer,
  //     'valueError',
  //     'value not acceptable',
  //     options.arrayExpressionPointer
  //   )
  // }
})
