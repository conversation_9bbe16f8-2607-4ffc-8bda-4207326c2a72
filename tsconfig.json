{"extends": "./node_modules/adonis-preset-ts/tsconfig", "include": ["**/*"], "exclude": ["node_modules", "build"], "compilerOptions": {"outDir": "build", "rootDir": "./", "sourceMap": true, "paths": {"App/*": ["./app/*"], "Config/*": ["./config/*"], "Contracts/*": ["./contracts/*"], "Database/*": ["./database/*"]}, "types": ["@adonisjs/core", "@adonisjs/repl", "@adonisjs/session", "@adonisjs/view", "@adonisjs/shield", "@adonisjs/lucid", "@adonisjs/auth", "@adonisjs/lucid-slugify", "adonis-lucid-filter", "@adonisjs/mail", "adonis5-scheduler"]}}