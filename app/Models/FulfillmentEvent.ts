import { DateTime } from 'luxon'
import { BaseModel, belongsTo, BelongsTo, column } from '@ioc:Adonis/Lucid/Orm'
import Fulfillment from './Fulfillment'
import Order from './Order'

export default class FulfillmentEvent extends BaseModel {
  @column({ isPrimary: true })
  public id: number

  @column()
  public address1: string

  @column()
  public city: string

  @column()
  public province: string

  @column()
  public country: string

  @column()
  public zip: string

  @column()
  public fulfillmentId: number

  @belongsTo(() => Fulfillment)
  public fulfillment: BelongsTo<typeof Fulfillment>

  @column()
  public orderId: number

  @belongsTo(() => Order)
  public order: BelongsTo<typeof Order>

  @column.dateTime()
  public happenedAt: DateTime

  @column.dateTime()
  public estimatedDeliveryAt: DateTime

  @column()
  public latitude: string

  @column()
  public longitude: string

  @column()
  public message: string

  @column()
  public status: string
  // label_printed: A label for the shipment was purchased and printed.
  // label_purchased: A label for the shipment was purchased, but not printed.
  // attempted_delivery: Delivery of the shipment was attempted, but unable to be completed.
  // ready_for_pickup: The shipment is ready for pickup at a shipping depot.
  // confirmed: The carrier is aware of the shipment, but hasn't received it yet.
  // in_transit: The shipment is being transported between shipping facilities on the way to its destination.
  // out_for_delivery: The shipment is being delivered to its final destination.
  // delivered: The shipment was succesfully delivered.
  // failure: Something went wrong when pulling tracking information for the shipment, such as the tracking number was invalid or the shipment was canceled.

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime
}
