import { Filterable } from '@ioc:Adonis/Addons/LucidFilter'
import { compose } from '@ioc:Adonis/Core/Helpers'
import { DateTime } from 'luxon'
import { BaseModel, column, belongsTo, BelongsTo } from '@ioc:Adonis/Lucid/Orm'
import Customer from './Customer'
import BlacklistFilter from './Filters/BlacklistFilter'

export default class Blacklist extends compose(BaseModel, Filterable) {
  public static $filter = () => BlacklistFilter

  @column({ isPrimary: true })
  public id: number

  @column()
  public customerId: number

  @belongsTo(() => Customer, { foreignKey: 'customerId' })
  public customer: BelongsTo<typeof Customer>

  //TODO: remove these and directly aggregate from orders instead?
  @column()
  public email: string

  // @column()
  // public firstName: string

  // @column()
  // public lastName: string

  @column()
  public name: string

  @column()
  public address: string

  @column()
  public apartment: string

  @column()
  public company: string

  @column()
  public remark: string

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime
}
