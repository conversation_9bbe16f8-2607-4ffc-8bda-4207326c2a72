import { DateTime } from 'luxon'
import { BaseModel, BelongsTo, belongsTo, column } from '@ioc:Adonis/Lucid/Orm'
import User from './User'

export default class Admin extends BaseModel {
  @column({ isPrimary: true })
  public id: number

  @column()
  public userId: number

  @belongsTo(() => User, {})
  public user: BelongsTo<typeof User>

  @column()
  public type: number

  @column()
  public productsAccess: boolean

  @column()
  public reviewsAccess: boolean

  @column()
  public userPermissionsAccess: boolean

  @column()
  public usersAccess: boolean

  @column()
  public stocksAccess: boolean

  @column()
  public eventsAccess: boolean

  @column()
  public warehousesAccess: boolean

  @column()
  public returnsAccess: boolean

  @column()
  public sourcesAccess: boolean

  @column()
  public ordersAccess: boolean

  @column()
  public ordersEditAccess: boolean

  @column()
  public customersAccess: boolean

  @column()
  public blacklistsAccess: boolean

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime
}
