import { compose } from '@ioc:Adonis/Core/Helpers'
import { DateTime } from 'luxon'
import {
  BaseModel,
  belongsTo,
  BelongsTo,
  column,
  computed,
  hasMany,
  HasMany,
} from '@ioc:Adonis/Lucid/Orm'
import Order from './Order'
import FulfillmentStatus from './FulfillmentStatus'
import FulfillmentLineItem from './FulfillmentLineItem'
import { Filterable } from '@ioc:Adonis/Addons/LucidFilter'
import FulfillmentFilter from './Filters/FulfillmentFilter'

export default class Fulfillment extends compose(BaseModel, Filterable) {
  public static $filter = () => FulfillmentFilter

  @column({ isPrimary: true })
  public id: number

  // @hasMany(() => LineItem, { foreignKey: 'fulfillmentId' })
  // public lineItems: HasMany<typeof LineItem>

  @hasMany(() => FulfillmentLineItem, { serializeAs: null, foreignKey: 'fulfillmentId' })
  public fulfillmentLineItems: HasMany<typeof FulfillmentLineItem>

  @computed()
  public get line_items() {
    if (!this.$preloaded['fulfillmentLineItems']) return null
    return this.fulfillmentLineItems.map((item) => {
      return {
        ...item?.lineItem?.serialize(),
        fulfillable_quantity: undefined,
        quantity: Math.abs(item?.event?.quantity),
      }
    })
  }

  // TODO: might conflict with Event?
  // @hasMany(() => FulfillmentEvent, {
  //   serializeAs: 'fulfillment_events',
  //   foreignKey: 'fulfillmentId',
  // })
  // public fulfillmentEvents: HasMany<typeof FulfillmentEvent>

  @column()
  public name: string

  // @column({
  //   consume: (value: boolean) => Boolean(value),
  // })
  // public notifyUser: boolean

  @column()
  public orderId: number

  @belongsTo(() => Order)
  public order: BelongsTo<typeof Order>

  @column({
    prepare: (value: object) => JSON.stringify(value),
    consume: (value: string) => JSON.parse(value),
  })
  public receipt: object

  @column()
  public service: string

  @column()
  public fulfillmentStatusId: number

  @belongsTo(() => FulfillmentStatus, {
    foreignKey: 'fulfillmentStatusId',
  })
  public status: BelongsTo<typeof FulfillmentStatus>

  // @column()
  // public shipmentStatus: string
  // label_printed: A label for the shipment was purchased and printed.
  // label_purchased: A label for the shipment was purchased, but not printed.
  // attempted_delivery: Delivery of the shipment was attempted, but unable to be completed.
  // ready_for_pickup: The shipment is ready for pickup at a shipping depot.
  // confirmed: The carrier is aware of the shipment, but hasn't received it yet.
  // in_transit: The shipment is being transported between shipping facilities on the way to its destination.
  // out_for_delivery: The shipment is being delivered to its final destination.
  // delivered: The shipment was succesfully delivered.
  // failure: Something went wrong when pulling tracking information for the shipment, such as the tracking number was invalid or the shipment was canceled.

  // @column()
  // public status: string
  // pending: The fulfillment is pending.
  // open: The fulfillment has been acknowledged by the service and is in processing.
  // success: The fulfillment was successful.
  // completed: The fulfillment was completed/order received by buyer.
  // rated: The fulfillment was rated.
  // cancelled: The fulfillment was cancelled.
  // return: The fulfillment was return.
  // error: There was an error with the fulfillment request.
  // failure: The fulfillment request failed.

  @column()
  public trackingCompanyName: string

  @column()
  public trackingNumber: string

  @column()
  public trackingUrls: string

  @column()
  public trackingUrl: string

  // @column()
  // public note: string

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime
}
