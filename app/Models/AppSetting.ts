import { DateTime } from 'luxon'
import { BaseModel, BelongsTo, belongsTo, column } from '@ioc:Adonis/Lucid/Orm'
import FulfillmentSetting from './FulfillmentSetting'
import YamatoSetting from './YamatoSetting'
import ShopifySetting from './ShopifySetting'

export default class AppSetting extends BaseModel {
  @column({ isPrimary: true })
  public id: number

  @column()
  public fulfillmentSettingId: number

  @belongsTo(() => FulfillmentSetting, {
    foreignKey: 'fulfillmentSettingId',
    serializeAs: 'fulfillment_setting',
  })
  public fulfillmentSetting: BelongsTo<typeof FulfillmentSetting>

  @column()
  public yamatoSettingId: number

  @belongsTo(() => YamatoSetting, {
    foreignKey: 'yamatoSettingId',
    serializeAs: 'yamato_setting',
  })
  public yamatoSetting: BelongsTo<typeof YamatoSetting>

  @column()
  public shopifySettingId: number

  @belongsTo(() => ShopifySetting, {
    foreignKey: 'shopifySettingId',
    serializeAs: 'shopify_setting',
  })
  public shopifySetting: BelongsTo<typeof ShopifySetting>

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime
}
