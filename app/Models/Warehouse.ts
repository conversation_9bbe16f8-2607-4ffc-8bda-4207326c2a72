import { DateTime } from 'luxon'
import { BaseModel, column } from '@ioc:Adonis/Lucid/Orm'
import { compose } from '@ioc:Adonis/Core/Helpers'
import { Filterable } from '@ioc:Adonis/Addons/LucidFilter'
import EventFilter from './Filters/EventFilter'

export default class Warehouse extends compose(BaseModel, Filterable) {
  public static $filter = () => EventFilter

  @column({ isPrimary: true })
  public id: number

  @column()
  public warehouseName: string

  @column()
  public location: string

  @column()
  public address: string

  @column()
  public isDefault: boolean

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime
}
