export interface YamatoLabelCSV {
  'お客様管理番号': string
  '送り状種類': string
  'クール区分': string
  '伝票番号': string
  '出荷予定日': string
  'お届け予定（指定）日'?: string
  '配達時間帯': string
  'お届け先コード'?: string
  'お届け先電話番号': string
  'お届け先電話番号枝番'?: string
  'お届け先郵便番号': string
  'お届け先住所': string
  'お届け先住所（アパートマンション名）'?: string
  'お届け先会社・部門名１'?: string
  'お届け先会社・部門名２'?: string
  'お届け先名': string
  'お届け先名略称カナ'?: string
  '敬称'?: string
  'ご依頼主コード'?: string
  'ご依頼主電話番号': string
  'ご依頼主電話番号枝番'?: string
  'ご依頼主郵便番号': string
  'ご依頼主住所': string
  'ご依頼主住所（アパートマンション名）'?: string
  'ご依頼主名': string
  'ご依頼主略称カナ'?: string
  '品名コード１'?: string
  '品名１'?: string
  '品名コード２'?: string
  '品名２'?: string
  '荷扱い１'?: string
  '荷扱い２'?: string
  '記事'?: string
  'コレクト代金引換額（税込）'?: string
  'コレクト内消費税額等'?: string
  '営業所止置き'?: string
  '営業所コード'?: string
  '発行枚数': string
  '個数口枠の印字'?: string
  'ご請求先顧客コード': string
  'ご請求先分類コード'?: string
  '運賃管理番号': string
}
