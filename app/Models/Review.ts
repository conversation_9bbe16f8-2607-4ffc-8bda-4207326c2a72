import { Filterable } from '@ioc:Adonis/Addons/LucidFilter'
import { compose } from '@ioc:Adonis/Core/Helpers'
import Customer from 'App/Models/Customer'
import { DateTime } from 'luxon'
import { BaseModel, BelongsTo, belongsTo, column } from '@ioc:Adonis/Lucid/Orm'
import Product from './Product'
import ReviewFilter from './Filters/ReviewFilter'

export default class Review extends compose(BaseModel, Filterable) {
  public static $filter = () => ReviewFilter

  @column({ isPrimary: true })
  public id: number

  @column()
  public title: string

  @column()
  public content: string

  @column()
  public score: number

  @column()
  public votesUp: number

  @column()
  public votesDown: number

  @column()
  public sentimental: number

  @column()
  public productId: number

  @column()
  public published: boolean

  @column()
  public shopifyId: string

  @column.dateTime()
  public orderDate: DateTime

  @belongsTo(() => Product, { foreignKey: 'productId' })
  public product: BelongsTo<typeof Product>

  @column()
  public customerId: number

  @belongsTo(() => Customer, { foreignKey: 'customerId' })
  public customer: BelongsTo<typeof Customer>

  @column({ serializeAs: null })
  public wasDeleted: boolean

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime
}
