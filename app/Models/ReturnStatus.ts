import { compose } from '@ioc:Adonis/Core/Helpers'
import { DateTime } from 'luxon'
import { BaseModel, belongsTo, column, BelongsTo } from '@ioc:Adonis/Lucid/Orm'
import Event from './Event'
import { Filterable } from '@ioc:Adonis/Addons/LucidFilter'
import ReturnStatusFilter from './Filters/ReturnStatusFilter'

export default class ReturnStatus extends compose(BaseModel, Filterable) {
  public static $filter = () => ReturnStatusFilter

  @column({ isPrimary: true })
  public id: number

  @column()
  public eventId: number

  @belongsTo(() => Event, { foreignKey: 'eventId' })
  public event: BelongsTo<typeof Event>

  @column()
  public isDefect: boolean

  @column()
  public returnReason: string

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime
}
