import { compose } from '@ioc:Adonis/Core/Helpers'
import { DateTime } from 'luxon'
import { BaseModel, column, HasMany, hasMany } from '@ioc:Adonis/Lucid/Orm'
import { Filterable } from '@ioc:Adonis/Addons/LucidFilter'
import PurchaseOrderFilter from 'App/Models/Filters/PurchaseOrderFilter'
import PurchaseOrderItem from 'App/Models/PurchaseOrderItem'
import PurchaseOrderFulfillment from 'App/Models/PurchaseOrderFulfillment'

export enum PurchaseOrderStatus {
  PENDING = 'pending',
  CANCELLED = 'cancelled',
  FULFILLED = 'fulfilled',
}

export default class PurchaseOrder extends compose(BaseModel, Filterable) {
  public static $filter = () => PurchaseOrderFilter

  @column({ isPrimary: true })
  public id: number

  @column()
  public receiverEmail: string

  @column()
  public remark: string | null

  @column()
  public status: PurchaseOrderStatus

  @hasMany(() => PurchaseOrderItem, {
    foreignKey: 'purchaseOrderId',
  })
  public items: HasMany<typeof PurchaseOrderItem>

  @hasMany(() => PurchaseOrderFulfillment, {
    foreignKey: 'purchaseOrderId',
  })
  public fulfillments: HasMany<typeof PurchaseOrderFulfillment>

  @column()
  public firstName: string

  @column()
  public lastName: string

  @column()
  public phone: string

  @column()
  public zip: string

  @column()
  public address: string

  @column()
  public apartment: string

  @column()
  public company: string

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime
}
