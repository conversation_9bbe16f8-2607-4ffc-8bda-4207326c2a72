import Stock from 'App/Models/Stock'
import { DateTime } from 'luxon'
import {
  BaseModel,
  belongsTo,
  BelongsTo,
  column,
  computed,
  HasMany,
  hasMany,
} from '@ioc:Adonis/Lucid/Orm'
import { compose } from '@ioc:Adonis/Core/Helpers'
import ProductVariantFilter from './Filters/ProductVariantFilter'
import { Filterable } from '@ioc:Adonis/Addons/LucidFilter'
import Product from './Product'

export default class ProductVariant extends compose(BaseModel, Filterable) {
  public static $filter = () => ProductVariantFilter

  @column({ isPrimary: true })
  public id: number

  @column()
  public variantName: string

  @column()
  public sku: string

  @column()
  public amazonSku: string

  @column()
  public shopifySku: string

  @column()
  public productId: number

  @belongsTo(() => Product, { foreignKey: 'productId' })
  public product: BelongsTo<typeof Product>

  @hasMany(() => Stock, { foreignKey: 'variantId' })
  public stocks: HasMany<typeof Stock>

  @column()
  public price: number

  @column()
  public barcode: string

  @column()
  public weight: number

  @column({ columnName: 'option_1', serializeAs: 'option_1' })
  public option1: string

  @column({ columnName: 'option_2', serializeAs: 'option_2' })
  public option2: string

  @column({ columnName: 'option_3', serializeAs: 'option_3' })
  public option3: string

  @column()
  public shopifyStockId: string

  @column()
  public shopifyLocationId: string

  @column()
  public shopifyId: string

  @column()
  public availability: boolean

  @computed({ serializeAs: 'fullname' })
  public get fullname() {
    return `${this.product?.productName ?? ''} - ${this.variantName}`
  }

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime
}
