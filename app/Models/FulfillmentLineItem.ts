import { DateTime } from 'luxon'
import { BaseModel, BelongsTo, belongsTo, column } from '@ioc:Adonis/Lucid/Orm'
import LineItem from './LineItem'
import Fulfillment from './Fulfillment'
import Event from './Event'

export default class FulfillmentLineItem extends BaseModel {
  @column({ isPrimary: true })
  public id: number

  @column()
  public lineItemId: number

  @belongsTo(() => LineItem, { foreignKey: 'lineItemId' })
  public lineItem: BelongsTo<typeof LineItem>

  @column()
  public fulfillmentId: number

  @belongsTo(() => Fulfillment, { foreignKey: 'fulfillmentId' })
  public fulfillment: BelongsTo<typeof Fulfillment>

  @column()
  public eventId: number

  @belongsTo(() => Event, { foreignKey: 'eventId' })
  public event: BelongsTo<typeof Event>

  @column()
  public quantity: number

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime
}
