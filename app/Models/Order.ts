import { compose } from '@ioc:Adonis/Core/Helpers'
import { DateTime } from 'luxon'
import {
  BaseModel,
  column,
  belongsTo,
  BelongsTo,
  manyToMany,
  ManyToMany,
  hasMany,
  HasMany,
  hasOne,
  HasOne,
  computed,
} from '@ioc:Adonis/Lucid/Orm'
// import FulfillmentStatus from './FulfillmentStatus'
import TransactionStatus from './TransactionStatus'
import Event from './Event'
import Customer from './Customer'
import { Filterable } from '@ioc:Adonis/Addons/LucidFilter'
import OrderFilter from './Filters/OrderFilter'
import ShopPlatform from './ShopPlatform'
import Fulfillment from './Fulfillment'
import LineItem from './LineItem'
import OrderStatus from './OrderStatus'
import FulfillmentStatus from './FulfillmentStatus'

export default class Order extends compose(BaseModel, Filterable) {
  public static $filter = () => OrderFilter
  @column({ isPrimary: true })
  public id: number

  //shopify order name, e.g. #1001
  @column()
  public name: string

  @manyToMany(() => Event, { pivotTable: 'event_orders' })
  public event: ManyToMany<typeof Event>

  @column()
  public platformOrderId: string

  @column()
  public firstName: string

  @column()
  public lastName: string

  @column()
  public phone: string

  @column()
  public customerId: number

  @belongsTo(() => Customer, { foreignKey: 'customerId' })
  public customer: BelongsTo<typeof Customer>

  @column()
  public zip: string

  @column()
  public address: string

  @column()
  public apartment: string

  @column()
  public company: string

  // TODO: to remove
  @column()
  public tracking: string

  // TODO: to remove
  @column()
  public trackingUrl: string

  // TODO: to remove
  @column()
  public trackingCompanyName: string

  @hasMany(() => Fulfillment)
  public fulfillments: HasMany<typeof Fulfillment>

  @column()
  public fulfillmentStatusId: number

  @belongsTo(() => FulfillmentStatus, {
    foreignKey: 'fulfillmentStatusId',
    serializeAs: 'fulfillment_status',
  })
  public fulfillmentStatus: BelongsTo<typeof FulfillmentStatus>

  @column()
  public orderStatusId: number

  @belongsTo(() => OrderStatus, {
    foreignKey: 'orderStatusId',
  })
  public status: BelongsTo<typeof OrderStatus>

  @column()
  public transactionStatusId: number

  @belongsTo(() => TransactionStatus, {
    foreignKey: 'transactionStatusId',
    serializeAs: 'transaction_status',
  })
  public transactionStatus: BelongsTo<typeof TransactionStatus>

  @column()
  public shopPlatformId: number

  @belongsTo(() => ShopPlatform, { foreignKey: 'shopPlatformId', serializeAs: 'shop_platform' })
  public shopPlatform: BelongsTo<typeof ShopPlatform>

  @hasMany(() => LineItem, { serializeAs: 'line_items', foreignKey: 'orderId' })
  public lineItems: HasMany<typeof LineItem>

  @hasOne(() => LineItem, { serializeAs: 'line_item', foreignKey: 'orderId' })
  public lineItem: HasOne<typeof LineItem>

  @column({
    prepare: (value: object) => JSON.stringify(value),
    consume: (value: string) => {
      return typeof value != 'string' ? value : JSON.parse(value)
    },
  })
  public noteObj: Array<any>

  @column({
    prepare: (value: object) => JSON.stringify(value),
    consume: (value: string) => {
      return typeof value != 'string' ? value : JSON.parse(value)
    },
  })
  public mailDelivery: Object

  @column()
  public generated: Boolean

  @column()
  public customerMemo: string

  @column()
  public operatorMemo: string

  @column.dateTime()
  public fulfilledAt: DateTime

  //TODO: add cancelled at

  @computed()
  public get total_amount() {
    if (!this.$preloaded['lineItems']) return null
    const totalAmount = this.lineItems.reduce(
      (prev, current) => prev + current.price * current.quantity,
      0
    )
    return totalAmount
  }

  @column.dateTime()
  public deliveryMailSentAt: DateTime

  @column.dateTime()
  public confirmationMailSentAt: DateTime

  @column.dateTime()
  public cancelledAt: DateTime

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime
}
