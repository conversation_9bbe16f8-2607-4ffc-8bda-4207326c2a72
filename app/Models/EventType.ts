import { DateTime } from 'luxon'
import { BaseModel, column } from '@ioc:Adonis/Lucid/Orm'

export default class EventType extends BaseModel {
  @column({ isPrimary: true })
  public id: number

  @column()
  public eventName: string

  @column()
  public isIn: <PERSON><PERSON><PERSON>

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime
}
