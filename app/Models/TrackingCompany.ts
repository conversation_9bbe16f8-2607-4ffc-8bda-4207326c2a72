import { DateTime } from 'luxon'
import { BaseModel, column } from '@ioc:Adonis/Lucid/Orm'

export default class TrackingCompany extends BaseModel {
  @column({ isPrimary: true })
  public id: number

  @column()
  public name: string

  @column()
  public officialName: string

  @column()
  public redirectLinkPrefix: string

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime
}

export function getTrackingCompanyForShopify(name: string) {
  if (name == '佐川ポスト投函') {
    return '日本郵便'
  }

  return name
}
