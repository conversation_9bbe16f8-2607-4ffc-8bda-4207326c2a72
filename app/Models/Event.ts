import { DateTime } from 'luxon'
import {
  BaseModel,
  belongsTo,
  column,
  BelongsTo,
  manyToMany,
  ManyToMany,
} from '@ioc:Adonis/Lucid/Orm'
import Stock from './Stock'
import EventType from './EventType'
import Status from './Status'
import Source from './Source'
import { compose } from '@ioc:Adonis/Core/Helpers'
import { Filterable } from '@ioc:Adonis/Addons/LucidFilter'
import EventFilter from './Filters/EventFilter'
import Order from './Order'

export default class Event extends compose(BaseModel, Filterable) {
  public static $filter = () => EventFilter

  @column({ isPrimary: true })
  public id: number

  @manyToMany(() => Order, { pivotTable: 'event_orders' })
  public order: ManyToMany<typeof Order>

  @column()
  public stockId: number

  @belongsTo(() => Stock, { foreignKey: 'stockId' })
  public stock: BelongsTo<typeof Stock>

  @column()
  public quantity: number

  @column()
  public eventTypeId: number

  @belongsTo(() => EventType, { foreignKey: 'eventTypeId' })
  public eventType: BelongsTo<typeof EventType>

  @column()
  public statusId: number

  @belongsTo(() => Status, { foreignKey: 'statusId' })
  public status: BelongsTo<typeof Status>

  //TODO: remove unnecessary fields
  @column()
  public sourceId: number

  @belongsTo(() => Source, { foreignKey: 'sourceId' })
  public source: BelongsTo<typeof Source>

  @column()
  public from: string

  @column()
  public to: string

  @column()
  public remark: string

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime
}
