//TODO: hard code for now
export const emailTemplates: EmailTemplate[] = [
  {
    id: 1,
    name: '【ERGOFINITEチェア用】 1通目',
    view: 'emails/ergofinite_flow/1',
    subject: 'Ergofinite by CASEFINITE - ご注文完了メール 【{{name}}】',
  },
  {
    id: 2,
    name: '【ERGOFINITEチェア用】 2通目',
    view: 'emails/ergofinite_flow/2',
    subject: 'Ergofinite by CASEFINITE - ご注文確定メール 【{{name}}】',
  },
  {
    id: 3,
    name: '【ERGOFINITEチェア用】 3通目',
    view: 'emails/ergofinite_flow/3',
    subject: 'Ergofinite by CASEFINITE - 出荷のお知らせ 【{{name}}】',
  },
  {
    id: 4,
    name: '【ERGOFINITEチェア用】 4通目',
    view: 'emails/ergofinite_flow/4',
    subject: 'Ergofinite by CASEFINITE - ご注文の商品の配送時間について 【{{name}}】',
  },
  {
    id: 5,
    name: '【CASEFINITE】配送先ご住所のご確認',
    view: 'emails/address_confirmation',
    subject: '※至急のご確認をお願いいたします。【CASEFINITE】配送先ご住所のご確認',
  },
]

export default interface EmailTemplate {
  id: number
  name: string
  view: string
  subject: string
}
