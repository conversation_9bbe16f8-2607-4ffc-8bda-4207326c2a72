import { BaseModelFilter } from '@ioc:Adonis/Addons/LucidFilter'
import { ModelQueryBuilderContract } from '@ioc:Adonis/Lucid/Orm'
import Review from '../Review'

export default class ReviewFilter extends BaseModelFilter {
  public $query: ModelQueryBuilderContract<typeof Review, Review>

  public id(value: any): void {
    this.$query.where('id', value)
  }

  public title(value: any): void {
    this.$query.where('title', 'LIKE', `%${value}%`)
  }

  public content(value: any): void {
    this.$query.where('content', 'LIKE', `%${value}%`)
  }

  public product(value: any): void {
    this.$query.where('product_id', value)
  }

  public shopifyProduct(value: any): void {
    this.$query.where('shopify_id', value)
  }

  public score(value: any): void {
    this.$query.where('score', `%${value}%`)
  }

  public votes_up(value: any): void {
    this.$query.where('votes_up', `%${value}%`)
  }

  public votes_down(value: any): void {
    this.$query.where('votes_up', `%${value}%`)
  }
}
