import { BaseModelFilter } from '@ioc:Adonis/Addons/LucidFilter'
import { ModelQueryBuilderContract } from '@ioc:Adonis/Lucid/Orm'
import ReturnStatus from 'App/Models/ReturnStatus'

export default class ReturnStatusFilter extends BaseModelFilter {
  public $query: ModelQueryBuilderContract<typeof ReturnStatus, ReturnStatus>

  public id(value: any): void {
    this.$query.where('id', value)
  }

  public event_id(value: any): void {
    this.$query.where('event_id', value)
  }

  public is_defect(value: any): void {
    this.$query.where('is_defect', value)
  }

  public return_reason(value: any): void {
    this.$query.where('return_reason', 'LIKE', `%${value}%`)
  }
}
