import { BaseModelFilter } from '@ioc:Adonis/Addons/LucidFilter'
import { ModelQueryBuilderContract } from '@ioc:Adonis/Lucid/Orm'
import PurchaseOrderFulfillment from 'App/Models/PurchaseOrderFulfillment'

export default class PurchaseOrderFulfillmentFilter extends BaseModelFilter {
  public $query: ModelQueryBuilderContract<
    typeof PurchaseOrderFulfillment,
    PurchaseOrderFulfillment
  >
  public id(value: any): void {
    this.$query.andWhere('purchase_order_fulfillments.id', value)
  }

  public name(value: any): void {
    this.$query.andWhere('purchase_order_fulfillments.name', 'LIKE', `%${value}%`)
  }

  public status(value: any): void {
    this.$query.andWhere('purchase_order_fulfillments.fulfillment_status_id', value)
  }

  public trackingNumber(value: any): void {
    this.$query.andWhere('purchase_order_fulfillments.tracking_number', value)
  }

  public trackingNumberGte(value: any): void {
    this.$query.andWhere('purchase_order_fulfillments.tracking_number', '>=', value)
  }

  public trackingNumberGt(value: any): void {
    this.$query.andWhere('purchase_order_fulfillments.tracking_number', '>', value)
  }

  public trackingCompanyName(value: any): void {
    this.$query.andWhere('tracking_company_name', 'LIKE', `%${value}%`)
  }

  public trackingCompanyNames(value: string): void {
    const names = value.split(',')
    this.$query.andWhereIn('tracking_company_name', names)
  }
}
