import { BaseModelFilter } from '@ioc:Adonis/Addons/LucidFilter'
import { ModelQueryBuilderContract } from '@ioc:Adonis/Lucid/Orm'
import Product from 'App/Models/Product'

export default class ProductFilter extends BaseModelFilter {
  public $query: ModelQueryBuilderContract<typeof Product, Product>

  public id(value: any): void {
    this.$query.where('id', value)
  }

  public sku(value: any): void {
    this.$query.where('sku', value)
  }

  public productName(value: any): void {
    this.$query.where('product_name', 'LIKE', `%${value}%`)
  }

  public shortDescription(value: any): void {
    this.$query.where('short_description', 'LIKE', `%${value}%`)
  }

  // public totalVariant(value: any): void {}
}
