import { BaseModelFilter } from '@ioc:Adonis/Addons/LucidFilter'
import { ModelQueryBuilderContract } from '@ioc:Adonis/Lucid/Orm'
import ProductVariant from 'App/Models/ProductVariant'

export default class ProductVariantFilter extends BaseModelFilter {
  public $query: ModelQueryBuilderContract<typeof ProductVariant, ProductVariant>

  public id(value: any): void {
    this.$query.where('id', value)
  }

  public sku(value: any): void {
    this.$query.where('sku', value)
  }

  public name(value: any): void {
    this.$query.where('variant_name', 'LIKE', `%${value}%`)
  }

  public search(value: any): void {
    this.$query
      .join('products', 'products.id', '=', 'product_variants.product_id')
      .where('variant_name', 'LIKE', `%${value}%`)
      .orWhere('product_name', 'LIKE', `%${value}%`)
  }
}
