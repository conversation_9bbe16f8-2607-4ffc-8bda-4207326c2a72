import { BaseModelFilter } from '@ioc:Adonis/Addons/LucidFilter'
import { ModelQueryBuilderContract } from '@ioc:Adonis/Lucid/Orm'
import Warehouse from 'App/Models/Warehouse'

export default class WarehouseFilter extends BaseModelFilter {
  public $query: ModelQueryBuilderContract<typeof Warehouse, Warehouse>

  public id(value: any): void {
    this.$query.where('id', value)
  }

  public name(value: any): void {
    this.$query.where('warehouse_name', 'LIKE', `%${value}%`)
  }

  public location(value: any): void {
    this.$query.where('location', 'LIKE', `%${value}%`)
  }
  public address(value: any): void {
    this.$query.where('address', 'LIKE', `%${value}%`)
  }
}
