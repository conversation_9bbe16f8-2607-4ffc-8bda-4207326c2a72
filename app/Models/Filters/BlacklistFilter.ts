import { BaseModelFilter } from '@ioc:Adonis/Addons/LucidFilter'
import { ModelQueryBuilderContract } from '@ioc:Adonis/Lucid/Orm'
import Blacklist from 'App/Models/Blacklist'

export default class BlacklistFilter extends BaseModelFilter {
  public $query: ModelQueryBuilderContract<typeof Blacklist, Blacklist>

  public id(value: any): void {
    this.$query.where('id', value)
  }

  public customer_id(value: any): void {
    this.$query.where('customer_id', value)
  }

  public remark(value: any): void {
    this.$query.where('remark', 'LIKE', `%${value}%`)
  }
}
