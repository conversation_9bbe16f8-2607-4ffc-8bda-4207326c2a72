import { BaseModelFilter } from '@ioc:Adonis/Addons/LucidFilter'
import { ModelQueryBuilderContract } from '@ioc:Adonis/Lucid/Orm'
import Source from '../Source'

export default class SourceFilter extends BaseModelFilter {
  public $query: ModelQueryBuilderContract<typeof Source, Source>

  public id(value: any): void {
    this.$query.where('id', value)
  }

  public name(value: any): void {
    this.$query.where('source_name', 'LIKE', `%${value}%`)
  }
}
