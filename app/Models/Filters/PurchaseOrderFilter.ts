import { BaseModelFilter } from '@ioc:Adonis/Addons/LucidFilter'
import { ModelQueryBuilderContract } from '@ioc:Adonis/Lucid/Orm'
import PurchaseOrder from 'App/Models/PurchaseOrder'

export default class PurchaseOrderFilter extends BaseModelFilter {
  public $query: ModelQueryBuilderContract<typeof PurchaseOrder, PurchaseOrder>

  public id(value: any): void {
    this.$query.andWhere('purchase_orders.id', value)
  }

  public sku(value: any): void {
    this.$query.where('purchase_order_items.sku', value)
  }

  public name(value: any): void {
    this.$query.where((query) => {
      query
        .where('purchase_orders.first_name', 'LIKE', `%${value}%`)
        .orWhere('purchase_orders.last_name', 'LIKE', `%${value}%`)
    })
  }

  public receiverEmail(value: any): void {
    this.$query.andWhere('purchase_orders.receiver_email', 'LIKE', `%${value}%`)
  }

  public remark(value: any): void {
    this.$query.andWhere('purchase_orders.remark', 'LIKE', `%${value}%`)
  }

  public tracking(value: any): void {
    this.$query.where('purchase_order_fulfillments.tracking_number', value)
  }

  public trackingGte(value: any): void {
    this.$query.andWhere('purchase_order_fulfillments.tracking_number', '>=', value)
  }

  public trackingGt(value: any): void {
    this.$query.andWhere('purchase_order_fulfillments.tracking_number', '>', value)
  }

  public address(value: any): void {
    this.$query
      .orWhere('purchase_orders.address', 'LIKE', `%${value}%`)
      .orWhere('purchase_orders.apartment', 'LIKE', `%${value}%`)
  }

  public status(value: any): void {
    this.$query.where('purchase_orders.status', value)
  }
}
