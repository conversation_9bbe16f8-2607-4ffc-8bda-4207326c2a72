import { BaseModelFilter } from '@ioc:Adonis/Addons/LucidFilter'
import { ModelQueryBuilderContract } from '@ioc:Adonis/Lucid/Orm'
import Stock from 'App/Models/Stock'

export default class StockFilter extends BaseModelFilter {
  public $query: ModelQueryBuilderContract<typeof Stock, Stock>

  public id(value: any): void {
    this.$query.where('id', value)
  }

  public variant_id(value: any): void {
    this.$query.where('variant_id', value)
  }

  public warehouse_id(value: any): void {
    this.$query.where('warehouse_id', value)
  }
}
