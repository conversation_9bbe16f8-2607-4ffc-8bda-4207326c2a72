import { BaseModelFilter } from '@ioc:Adonis/Addons/LucidFilter'
import { ModelQueryBuilderContract } from '@ioc:Adonis/Lucid/Orm'
import Customer from 'App/Models/Customer'

export default class CustomerFilter extends BaseModelFilter {
  public $query: ModelQueryBuilderContract<typeof Customer, Customer>

  public id(value: any): void {
    this.$query.where('id', value)
  }

  public email(value: any): void {
    this.$query.where('email', 'LIKE', `%${value}%`)
  }

  public name(value: any): void {
    this.$query.where('name', 'LIKE', `%${value}%`)
  }

  public phone(value: any): void {
    this.$query.where('phone', 'LIKE', `%${value}%`)
  }

  public search(value: any): void {
    this.$query.orWhere('name', 'LIKE', `%${value}%`).orWhere('email', 'LIKE', `%${value}%`)
  }
}
