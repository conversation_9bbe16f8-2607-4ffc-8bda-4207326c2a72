import Fulfillment from 'App/Models/Fulfillment'
import { BaseModelFilter } from '@ioc:Adonis/Addons/LucidFilter'
import { ModelQueryBuilderContract } from '@ioc:Adonis/Lucid/Orm'

export default class FulfillmentFilter extends BaseModelFilter {
  public $query: ModelQueryBuilderContract<typeof Fulfillment, Fulfillment>

  public id(value: any): void {
    this.$query.andWhere('fulfillments.id', value)
  }

  public name(value: any): void {
    this.$query.andWhere('fulfillments.name', 'LIKE', `%${value}%`)
  }

  public status(value: any): void {
    this.$query.andWhere('fulfillments.fulfillment_status_id', value)
  }

  public trackingNumber(value: any): void {
    this.$query.andWhere('fulfillments.tracking_number', value)
  }

  public trackingNumberGte(value: any): void {
    this.$query.andWhere('fulfillments.tracking_number', '>=', value)
  }

  public trackingNumberGt(value: any): void {
    this.$query.andWhere('fulfillments.tracking_number', '>', value)
  }

  public transactionStatus(value: any): void {
    this.$query.andWhere('transaction_status_id', value)
  }

  public trackingCompanyName(value: any): void {
    this.$query.andWhere('tracking_company_name', 'LIKE', `%${value}%`)
  }

  public trackingCompanyNames(value: string): void {
    const names = value.split(',')
    this.$query.andWhereIn('tracking_company_name', names)
  }
}
