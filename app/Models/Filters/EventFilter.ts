import { BaseModelFilter } from '@ioc:Adonis/Addons/LucidFilter'
import { ModelQueryBuilderContract } from '@ioc:Adonis/Lucid/Orm'
import Event from 'App/Models/Event'

export default class EventFilter extends BaseModelFilter {
  public $query: ModelQueryBuilderContract<typeof Event, Event>

  public id(value: any): void {
    this.$query.where('id', value)
  }

  public stockId(value: any): void {
    this.$query.where('stock_id', value)
  }

  public eventTypeId(value: any): void {
    this.$query.where('event_type_id', value)
  }

  public statusId(value: any): void {
    this.$query.where('status_id', value)
  }

  public sourceId(value: any): void {
    this.$query.where('source_id', value)
  }
  public remark(value: any): void {
    this.$query.where('remark', 'LIKE', `%${value}%`)
  }
}
