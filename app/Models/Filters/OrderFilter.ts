import Order from 'App/Models/Order'
import { BaseModelFilter } from '@ioc:Adonis/Addons/LucidFilter'
import { ModelQueryBuilderContract } from '@ioc:Adonis/Lucid/Orm'

export default class OrderFilter extends BaseModelFilter {
  public $query: ModelQueryBuilderContract<typeof Order, Order>

  public id(value: any): void {
    this.$query.where('orders.id', value)
  }

  public name(value: any): void {
    this.$query.where('orders.name', 'LIKE', `%${value}%`)
  }

  public platformOrderId(value: any): void {
    this.$query.where('orders.platform_order_id', 'LIKE', `%${value}%`)
  }

  public status(value: any): void {
    if (value == 1) {
      //not pending, cancelled or refunded
      this.$query.whereNotIn('transaction_status_id', [2, 4, 5]).andWhere('order_status_id', 1)
    } else if (value == 2) {
      //pending payment
      this.$query.whereIn('transaction_status_id', [2]).andWhere('order_status_id', 1)
    } else if (value == 3) {
      this.$query.where('order_status_id', 2)
    } else if (value == 4) {
      this.$query.where('order_status_id', 3)
    } else if (value == 5) {
      this.$query.whereNotIn('transaction_status_id', [2, 4, 5]).andWhere('order_status_id', 4)
    } else if (value == 6) {
      this.$query.where('order_status_id', 5)
    } else if (value == 7) {
      this.$query.where('order_status_id', 7)
    } else if (value == 8) {
      this.$query.where('order_status_id', 6)
    } else if (value == 9) {
      this.$query.where('order_status_id', 8)
    }
  }

  public customerEmail(value: any): void {
    this.$query.where('customers.email', 'LIKE', `%${value}%`)
  }

  public tracking(value: any): void {
    this.$query.where('fulfillments.tracking', value)
  }

  public trackingGte(value: any): void {
    this.$query.andWhere('fulfillments.tracking', '>=', value)
  }

  public trackingGt(value: any): void {
    this.$query.andWhere('fulfillments.tracking', '>', value)
  }

  public address(value: any): void {
    this.$query
      .orWhere('orders.address', 'LIKE', `%${value}%`)
      .orWhere('orders.apartment', 'LIKE', `%${value}%`)
  }

  public fulfillmentStatus(value: any): void {
    console.log(value)
    this.$query.where('fulfillment_status_id', value)
  }

  public transactionStatus(value: any): void {
    this.$query.where('transaction_status_id', value)
  }

  public trackingCompanyName(value: any): void {
    this.$query.where('tracking_company_name', 'LIKE', `%${value}%`)
  }

  public shopPlatformId(value: any): void {
    this.$query.where('shop_platform_id', value)
  }

  public sku(value: any): void {
    this.$query.where('shop_platform_id', value)
  }

  public variantName(value: any): void {
    this.$query.where('line_items.variant_title', 'LIKE', `%${value}%`)
  }

  public productName(value: any): void {
    this.$query.where('line_items.product_title', 'LIKE', `%${value}%`)
  }
}
