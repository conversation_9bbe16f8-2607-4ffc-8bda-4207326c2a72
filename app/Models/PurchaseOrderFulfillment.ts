import { compose } from '@ioc:Adonis/Core/Helpers'
import { DateTime } from 'luxon'
import { BaseModel, BelongsTo, belongsTo, column } from '@ioc:Adonis/Lucid/Orm'
import PurchaseOrder from 'App/Models/PurchaseOrder'
import FulfillmentStatus from 'App/Models/FulfillmentStatus'
import { Filterable } from '@ioc:Adonis/Addons/LucidFilter'
import PurchaseOrderFulfillmentFilter from 'App/Models/Filters/PurchaseOrderFulfillmentFilter'

export default class PurchaseOrderFulfillment extends compose(BaseModel, Filterable) {
  public static $filter = () => PurchaseOrderFulfillmentFilter

  @column({ isPrimary: true })
  public id: number

  @column()
  public purchaseOrderId: number

  @belongsTo(() => PurchaseOrder, {
    foreignKey: 'purchaseOrderId',
    serializeAs: 'purchase_order',
  })
  public purchaseOrder: BelongsTo<typeof PurchaseOrder>

  @column()
  public trackingCompanyName: string

  @column()
  public trackingNumber: string

  @column()
  public trackingUrls: string

  @column()
  public trackingUrl: string

  @column()
  public fulfillmentStatusId: number

  @belongsTo(() => FulfillmentStatus, {
    foreignKey: 'fulfillmentStatusId',
  })
  public status: BelongsTo<typeof FulfillmentStatus>

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime
}
