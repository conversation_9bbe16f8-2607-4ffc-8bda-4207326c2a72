import Review from 'App/Models/Review'
import { Filterable } from '@ioc:Adonis/Addons/LucidFilter'
import { compose } from '@ioc:Adonis/Core/Helpers'
import ProductVariant from 'App/Models/ProductVariant'
import { DateTime } from 'luxon'
import { BaseModel, column, computed, HasMany, hasMany } from '@ioc:Adonis/Lucid/Orm'
import ProductFilter from './Filters/ProductFilter'

export default class Product extends compose(BaseModel, Filterable) {
  public static $filter = () => ProductFilter
  @column({ isPrimary: true })
  public id: number

  @column()
  public productName: string

  @column()
  public shortDescription: string

  @column()
  public shopifyId: string

  @column()
  public sku: string

  @column({ columnName: 'option_1', serializeAs: 'option_1' })
  public option1: string

  @column({ columnName: 'option_2', serializeAs: 'option_2' })
  public option2: string

  @column({ columnName: 'option_3', serializeAs: 'option_3' })
  public option3: string

  @hasMany(() => ProductVariant, {
    foreignKey: 'productId',
  })
  public variants: HasMany<typeof ProductVariant>

  @hasMany(() => Review, {
    foreignKey: 'productId',
  })
  public reviews: HasMany<typeof Review>

  @computed({ serializeAs: 'avg_rating' })
  public get avgRating() {
    return this?.$extras?.avg_rating ?? 0
  }

  @computed({ serializeAs: 'max_rating' })
  public get maxRating() {
    return this?.$extras?.max_rating ?? 0
  }

  @computed({ serializeAs: 'min_rating' })
  public get minRating() {
    return this?.$extras?.min_rating ?? 0
  }

  @computed({ serializeAs: 'rating_count' })
  public get ratingCount() {
    return this?.$extras?.rating_count ?? 0
  }

  @column()
  public shopifySync: boolean

  @column()
  public amazonSync: boolean

  @computed({ serializeAs: 'total_variant' })
  public get totalVariant() {
    return this.variants?.length ?? 0
  }

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime
}
