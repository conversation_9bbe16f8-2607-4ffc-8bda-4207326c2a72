import { DateTime } from 'luxon'
import { BaseModel, column } from '@ioc:Adonis/Lucid/Orm'

export default class Delivery extends BaseModel {
  @column({ isPrimary: true })
  public id: number

  @column()
  public companyId: string

  @column()
  public iraiDateTime: DateTime

  @column()
  public clientIp: string

  @column()
  public tradingId: string

  @column()
  public reservePwd: string

  @column()
  public anonymityFlag: string

  @column()
  public invoiceKb: string

  @column()
  public payKb: string

  @column()
  public dstKb: string

  @column()
  public dstTel1: string

  @column()
  public dstTel2: string

  @column()
  public dstTel3: string

  @column()
  public dstZipCd: string

  @column()
  public dstAddress1: string

  @column()
  public dstAddress2: string

  @column()
  public dstAddress3: string

  @column()
  public dstAddress4: string

  @column()
  public dstCoNm: string

  @column()
  public dstDivNm: string

  @column()
  public dstLastNm: string

  @column()
  public dstFirstNm: string

  @column()
  public srcTel1: string

  @column()
  public srcTel2: string

  @column()
  public srcTel3: string

  @column()
  public srcZipCd: string

  @column()
  public srcAddress1: string

  @column()
  public srcAddress2: string

  @column()
  public srcAddress3: string

  @column()
  public srcAddress4: string

  @column()
  public srcCoNm: string

  @column()
  public srcDivNm: string

  @column()
  public srcLastNm: string

  @column()
  public srcFirstNm: string

  @column()
  public baggDesc2: string

  @column()
  public baggHandling1: string

  @column()
  public baggHandling2: string

  @column()
  public shipDate: Date

  @column()
  public delivDate: Date

  @column()
  public delivTime: string

  @column()
  public dstMailFlg: string

  @column()
  public dstMailFlgToukan: string

  @column()
  public srcMailFlg: string

  @column()
  public dstMailAddr: string

  @column()
  public dstMailKb: string

  @column()
  public dstJigyoshoNm: string

  @column()
  public dstJigyoshoCd: string

  @column()
  public shukaFlg: string

  @column()
  public shukaLastNm: string

  @column()
  public shukaFirstNm: string

  @column()
  public shukaLastNmKana: string

  @column()
  public shukaFirstNmKana: string

  @column()
  public shukaCoNm: string

  @column()
  public shukaCoNmKana: string

  @column()
  public shukaDivName: string

  @column()
  public shukaDivNameKana: string

  @column()
  public shukaTel1: string

  @column()
  public shukaTel2: string

  @column()
  public shukaTel3: string

  @column()
  public shukaMailAddr: string

  @column()
  public shukaZipCd: string

  @column()
  public shukaAddress1: string

  @column()
  public shukaAddress2: string

  @column()
  public shukaAddress3: string

  @column()
  public shukaAddress4: string

  @column()
  public shukaKosu: string

  @column()
  public shukaCoolKb: string

  @column()
  public shukaDate: Date

  @column()
  public shukaTime: string

  @column()
  public shukaComment: string

  @column()
  public shukaSizai: string

  @column()
  public trackingCd: string

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime
}
