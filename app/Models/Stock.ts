import { Filterable } from '@ioc:Adonis/Addons/LucidFilter'
import { compose } from '@ioc:Adonis/Core/Helpers'
import { DateTime } from 'luxon'
import { BaseModel, column, belongsTo, BelongsTo, computed } from '@ioc:Adonis/Lucid/Orm'
import Warehouse from './Warehouse'
import ProductVariant from './ProductVariant'
import StockFilter from './Filters/StockFilter'
import StockQuality from './StockQuality'

export default class Stock extends compose(BaseModel, Filterable) {
  public static $filter = () => StockFilter

  @column({ isPrimary: true })
  public id: number

  @column()
  public warehouseId: number

  @column()
  public variantId: number

  @column()
  public qualityId: number

  @belongsTo(() => Warehouse, { foreignKey: 'warehouseId' })
  public warehouse: BelongsTo<typeof Warehouse>

  @belongsTo(() => ProductVariant, { foreignKey: 'variantId' })
  public variant: BelongsTo<typeof ProductVariant>

  @belongsTo(() => StockQuality, { foreignKey: 'qualityId' })
  public quality: BelongsTo<typeof StockQuality>

  @column({ serializeAs: null })
  public wasDeleted: boolean

  @computed({ serializeAs: 'total_stocks' })
  public get totalStocks() {
    return this.$extras?.total_stocks ?? 0
  }

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime
}
