import { DateTime } from 'luxon'
import { BaseModel, column, computed, HasOne, hasOne } from '@ioc:Adonis/Lucid/Orm'
import AppSetting from './AppSetting'

export default class FulfillmentSetting extends BaseModel {
  @column({ isPrimary: true })
  public id: number

  @column()
  public settingName: string

  @column()
  public defaultItemName: string

  @column()
  public name: string

  @column()
  public phone: string

  @column()
  public postal: string

  @column()
  public province: string

  @column()
  public city: string

  @column({ columnName: 'address_1', serializeAs: 'address_1' })
  public address1: string

  @column({ columnName: 'address_2', serializeAs: 'address_2' })
  public address2: string

  @hasOne(() => AppSetting, {
    foreignKey: 'fulfillmentSettingId',
  })
  public appSetting: HasOne<typeof AppSetting>

  @computed({ serializeAs: 'is_default' })
  public get isDefault() {
    return this.$preloaded.appSetting !== null
  }

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime
}
