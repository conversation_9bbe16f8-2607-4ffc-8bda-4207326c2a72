import { DateTime } from 'luxon'
import { BaseModel, belongsTo, BelongsTo, column } from '@ioc:Adonis/Lucid/Orm'
import TrackingCompany from './TrackingCompany'

export default class WeightBasedShippingRule extends BaseModel {
  @column({ isPrimary: true })
  public id: number

  @column()
  public maxWeight: number

  @column()
  public trackingCompanyId: number

  @belongsTo(() => TrackingCompany)
  public trackingCompany: BelongsTo<typeof TrackingCompany>

  @column()
  public priority: number

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime
}