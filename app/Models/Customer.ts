import Order from 'App/Models/Order'
import { Filterable } from '@ioc:Adonis/Addons/LucidFilter'
import { compose } from '@ioc:Adonis/Core/Helpers'
import { DateTime } from 'luxon'
import { BaseModel, column, HasOne, hasOne, hasMany, HasMany } from '@ioc:Adonis/Lucid/Orm'
import CustomerFilter from './Filters/CustomerFilter'
import Blacklist from './Blacklist'

export default class Customer extends compose(BaseModel, Filterable) {
  public static $filter = () => CustomerFilter

  @column({ isPrimary: true })
  public id: number

  @column()
  public email: string

  @column()
  public name: string

  @column()
  public phone: string

  @hasOne(() => Blacklist, {
    foreignKey: 'customerId',
  })
  public blacklist: HasOne<typeof Blacklist>

  @hasMany(() => Order, {
    foreignKey: 'customerId',
  })
  public orders: HasMany<typeof Order>
  //TODO: remove user relationship

  // @column()
  // public userId: number

  // @belongsTo(() => User, {
  //   foreignKey: 'userId',
  // })
  // public user: BelongsTo<typeof User>

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime
}
