declare module Shopify {
  export interface BillingAddress {
    address1: string
    address2: string
    city: string
    company?: any
    country: string
    first_name: string
    last_name: string
    phone: string
    province: string
    zip: string
    name: string
    province_code: string
    country_code: string
    latitude: string
    longitude: string
  }

  export interface ClientDetails {
    accept_language: string
    browser_height: number
    browser_ip: string
    browser_width: number
    session_hash: string
    user_agent: string
  }

  export interface ShopMoney {
    amount: string
    currency_code: string
  }

  export interface PresentmentMoney {
    amount: string
    currency_code: string
  }

  export interface CurrentTotalDiscountsSet2 {
    shop_money: ShopMoney
    presentment_money: PresentmentMoney
  }

  export interface CurrentTotalDiscountsSet {
    current_total_discounts_set: CurrentTotalDiscountsSet2
  }

  export interface ShopMoney2 {
    amount: string
    currency_code: string
  }

  export interface PresentmentMoney2 {
    amount: string
    currency_code: string
  }

  export interface CurrentTotalDutiesSet2 {
    shop_money: ShopMoney2
    presentment_money: PresentmentMoney2
  }

  export interface CurrentTotalDutiesSet {
    current_total_duties_set: CurrentTotalDutiesSet2
  }

  export interface ShopMoney3 {
    amount: string
    currency_code: string
  }

  export interface PresentmentMoney3 {
    amount: string
    currency_code: string
  }

  export interface CurrentTotalPriceSet2 {
    shop_money: ShopMoney3
    presentment_money: PresentmentMoney3
  }

  export interface CurrentTotalPriceSet {
    current_total_price_set: CurrentTotalPriceSet2
  }

  export interface ShopMoney4 {
    amount: string
    currency_code: string
  }

  export interface PresentmentMoney4 {
    amount: string
    currency_code: string
  }

  export interface CurrentSubtotalPriceSet2 {
    shop_money: ShopMoney4
    presentment_money: PresentmentMoney4
  }

  export interface CurrentSubtotalPriceSet {
    current_subtotal_price_set: CurrentSubtotalPriceSet2
  }

  export interface ShopMoney5 {
    amount: string
    currency_code: string
  }

  export interface PresentmentMoney5 {
    amount: string
    currency_code: string
  }

  export interface CurrentTotalTaxSet2 {
    shop_money: ShopMoney5
    presentment_money: PresentmentMoney5
  }

  export interface CurrentTotalTaxSet {
    current_total_tax_set: CurrentTotalTaxSet2
  }

  export interface TaxExemptions {}

  export interface Addresses {}

  export interface DefaultAddress {}

  export interface Customer {
    id: number
    email: string
    accepts_marketing: boolean
    created_at: Date
    updated_at: Date
    first_name: string
    last_name: string
    state: string
    note?: any
    verified_email: boolean
    multipass_identifier?: any
    tax_exempt: boolean
    tax_exemptions: TaxExemptions
    phone: string
    tags: string
    currency: string
    addresses: Addresses
    admin_graphql_api_id: string
    default_address: DefaultAddress
  }

  export interface DiscountApplication {
    type: string
    title: string
    description: string
    value: string
    value_type: string
    allocation_method: string
    target_selection: string
    target_type: string
    code: string
  }

  export interface DiscountApplications {
    discount_applications: DiscountApplication[]
  }

  export interface DiscountCode {
    code: string
    amount: string
    type: string
  }

  export interface Fulfillment {
    created_at: Date
    id: number
    name: string
    order_id: number
    status: string
    tracking_company: string
    tracking_number: string
    tracking_url: string
    updated_at: Date
    line_items: LineItem[]
  }

  export interface ShopMoney6 {
    amount: string
    currency_code: string
  }

  export interface PresentmentMoney6 {
    amount: string
    currency_code: string
  }

  export interface PriceSet {
    shop_money: ShopMoney6
    presentment_money: PresentmentMoney6
  }

  export interface Property {
    name: string
    value: string
  }

  export interface ShopMoney7 {
    amount: string
    currency_code: string
  }

  export interface PresentmentMoney7 {
    amount: string
    currency_code: string
  }

  export interface PriceSet2 {
    shop_money: ShopMoney7
    presentment_money: PresentmentMoney7
  }

  export interface TaxLine {
    title: string
    price: string
    price_set: PriceSet2
    channel_liable: boolean
    rate: number
  }

  export interface ShopMoney8 {
    amount: string
    currency_code: string
  }

  export interface PresentmentMoney8 {
    amount: string
    currency_code: string
  }

  export interface TotalDiscountSet {
    shop_money: ShopMoney8
    presentment_money: PresentmentMoney8
  }

  export interface ShopMoney9 {
    amount: string
    currency_code: string
  }

  export interface PresentmentMoney9 {
    amount: string
    currency_code: string
  }

  export interface AmountSet {
    shop_money: ShopMoney9
    presentment_money: PresentmentMoney9
  }

  export interface DiscountAllocation {
    amount: string
    discount_application_index: number
    amount_set: AmountSet
  }

  export interface OriginLocation {
    id: number
    country_code: string
    province_code: string
    name: string
    address1: string
    address2: string
    city: string
    zip: string
  }

  export interface ShopMoney10 {
    amount: string
    currency_code: string
  }

  export interface PresentmentMoney10 {
    amount: string
    currency_code: string
  }

  export interface ShopMoney11 {
    amount: string
    currency_code: string
  }

  export interface PresentmentMoney11 {
    amount: string
    currency_code: string
  }

  export interface PriceSet3 {
    shop_money: ShopMoney11
    presentment_money: PresentmentMoney11
  }

  export interface TaxLine2 {
    title: string
    price: string
    rate: number
    price_set: PriceSet3
    channel_liable: boolean
  }

  export interface Duty {
    id: string
    harmonized_system_code: string
    country_code_of_origin: string
    shop_money: ShopMoney10
    presentment_money: PresentmentMoney10
    tax_lines: TaxLine2[]
    admin_graphql_api_id: string
  }

  export interface LineItem {
    fulfillable_quantity: number
    fulfillment_service: string
    fulfillment_status: string
    grams: number
    id: number
    price: string
    product_id: number
    quantity: number
    requires_shipping: boolean
    sku: string
    title: string
    variant_id: number
    variant_title: string
    vendor: string
    name: string
    gift_card: boolean
    price_set: PriceSet
    properties: Property[]
    taxable: boolean
    tax_lines: TaxLine[]
    total_discount: string
    total_discount_set: TotalDiscountSet
    discount_allocations: DiscountAllocation[]
    origin_location: OriginLocation
    duties: Duty[]
  }

  export interface NoteAttribute {
    name: string
    value: string
  }

  export interface ShopMoney12 {
    amount: string
    currency_code: string
  }

  export interface PresentmentMoney12 {
    amount: string
    currency_code: string
  }

  export interface OriginalTotalDutiesSet2 {
    shop_money: ShopMoney12
    presentment_money: PresentmentMoney12
  }

  export interface OriginalTotalDutiesSet {
    original_total_duties_set: OriginalTotalDutiesSet2
  }

  export interface PaymentDetails {
    avs_result_code: string
    credit_card_bin: string
    cvv_result_code: string
    credit_card_number: string
    credit_card_company: string
  }

  export interface PaymentSchedule {
    amount: number
    currency: string
    issued_at: Date
    due_at: Date
    completed_at: string
    expected_payment_method: string
  }

  export interface PaymentTerms {
    amount: number
    currency: string
    payment_terms_name: string
    payment_terms_type: string
    due_in_days: number
    payment_schedules: PaymentSchedule[]
  }

  export interface Refund {
    id: number
    order_id: number
    created_at: Date
    note?: any
    user_id?: any
    processed_at: Date
    refund_line_items: any[]
    transactions: any[]
    order_adjustments: any[]
  }

  export interface ShippingAddress {
    address1: string
    address2: string
    city: string
    company?: any
    country: string
    first_name: string
    last_name: string
    latitude: string
    longitude: string
    phone: string
    province: string
    zip: string
    name: string
    country_code: string
    province_code: string
  }

  export interface ShopMoney13 {
    amount: string
    currency_code: string
  }

  export interface PresentmentMoney13 {
    amount: string
    currency_code: string
  }

  export interface PriceSet4 {
    shop_money: ShopMoney13
    presentment_money: PresentmentMoney13
  }

  export interface ShopMoney14 {
    amount: string
    currency_code: string
  }

  export interface PresentmentMoney14 {
    amount: string
    currency_code: string
  }

  export interface DiscountedPriceSet {
    shop_money: ShopMoney14
    presentment_money: PresentmentMoney14
  }

  export interface ShippingLine {
    code: string
    price: string
    price_set: PriceSet4
    discounted_price: string
    discounted_price_set: DiscountedPriceSet
    source: string
    title: string
    tax_lines: any[]
    carrier_identifier: string
    requested_fulfillment_service_id: string
  }

  export interface ShopMoney15 {
    amount: string
    currency_code: string
  }

  export interface PresentmentMoney15 {
    amount: string
    currency_code: string
  }

  export interface SubtotalPriceSet {
    shop_money: ShopMoney15
    presentment_money: PresentmentMoney15
  }

  export interface TaxLine3 {
    price: number
    rate: number
    title: string
    channel_liable: boolean
  }

  export interface ShopMoney16 {
    amount: string
    currency_code: string
  }

  export interface PresentmentMoney16 {
    amount: string
    currency_code: string
  }

  export interface TotalDiscountsSet {
    shop_money: ShopMoney16
    presentment_money: PresentmentMoney16
  }

  export interface ShopMoney17 {
    amount: string
    currency_code: string
  }

  export interface PresentmentMoney17 {
    amount: string
    currency_code: string
  }

  export interface TotalLineItemsPriceSet {
    shop_money: ShopMoney17
    presentment_money: PresentmentMoney17
  }

  export interface ShopMoney18 {
    amount: string
    currency_code: string
  }

  export interface PresentmentMoney18 {
    amount: string
    currency_code: string
  }

  export interface TotalPriceSet {
    shop_money: ShopMoney18
    presentment_money: PresentmentMoney18
  }

  export interface ShopMoney19 {
    amount: string
    currency_code: string
  }

  export interface PresentmentMoney19 {
    amount: string
    currency_code: string
  }

  export interface TotalShippingPriceSet {
    shop_money: ShopMoney19
    presentment_money: PresentmentMoney19
  }

  export interface ShopMoney20 {
    amount: string
    currency_code: string
  }

  export interface PresentmentMoney20 {
    amount: string
    currency_code: string
  }

  export interface TotalTaxSet {
    shop_money: ShopMoney20
    presentment_money: PresentmentMoney20
  }

  export interface OrderStatusUrl {
    order_status_url: string
  }

  export interface Order {
    app_id: number
    billing_address: BillingAddress
    browser_ip: string
    buyer_accepts_marketing: boolean
    cancel_reason: string
    cancelled_at?: any
    cart_token: string
    checkout_token: string
    client_details: ClientDetails
    closed_at: Date
    created_at: Date
    currency: string
    current_total_discounts: string
    current_total_discounts_set: CurrentTotalDiscountsSet
    current_total_duties_set: CurrentTotalDutiesSet
    current_total_price: string
    current_total_price_set: CurrentTotalPriceSet
    current_subtotal_price: string
    current_subtotal_price_set: CurrentSubtotalPriceSet
    current_total_tax: string
    current_total_tax_set: CurrentTotalTaxSet
    customer: Customer
    customer_locale: string
    discount_applications: DiscountApplications
    discount_codes: DiscountCode[]
    email: string
    estimated_taxes: boolean
    financial_status:
      | 'pending'
      | 'authorized'
      | 'partially_paid'
      | 'paid'
      | 'partially_refunded'
      | 'refunded'
      | 'voided'
    fulfillments: Fulfillment[]
    fulfillment_status: 'fulfilled' | 'null' | 'partial' | 'restocked'
    gateway: string
    id: number
    landing_site: string
    line_items: LineItem[]
    location_id: number
    name: string
    note: string
    note_attributes: NoteAttribute[]
    number: number
    order_number: number
    original_total_duties_set: OriginalTotalDutiesSet
    payment_details: PaymentDetails
    payment_terms: PaymentTerms
    payment_gateway_names: string[]
    phone: string
    presentment_currency: string
    processed_at: Date
    processing_method: string
    referring_site: string
    refunds: Refund[]
    shipping_address: ShippingAddress
    shipping_lines: ShippingLine[]
    source_name: string
    source_identifier: string
    source_url: string
    subtotal_price: number
    subtotal_price_set: SubtotalPriceSet
    tags: string
    tax_lines: TaxLine3[]
    taxes_included: boolean
    test: boolean
    token: string
    total_discounts: string
    total_discounts_set: TotalDiscountsSet
    total_line_items_price: string
    total_line_items_price_set: TotalLineItemsPriceSet
    total_outstanding: string
    total_price: string
    total_price_set: TotalPriceSet
    total_shipping_price_set: TotalShippingPriceSet
    total_tax: string
    total_tax_set: TotalTaxSet
    total_tip_received: string
    total_weight: number
    updated_at: Date
    user_id: number
    order_status_url: OrderStatusUrl
  }

  export interface InventoryLevel {
    available: number
    inventory_item_id: string
    location_id: string
    updated_at: string
  }

  export interface FulfillmentOrder {
    id: number
    shop_id: number
    order_id: number
    assigned_location_id: number
    request_status: string
    status: string
    supported_actions: string[]
    destination: {
      id: number
      address1: string
      address2: string
      city: string
      company?: string
      country: string
      email: string
      first_name: string
      last_name: string
      phone: string
      province: string
      zip: string
    }
    line_items: [
      {
        id: number
        shop_id: number
        fulfillment_order_id: number
        quantity: number
        line_item_id: number
        inventory_item_id: number
        fulfillable_quantity: number
        variant_id: number
      }
    ]
    fulfill_at?: string
    fulfill_by?: string
    created_at: string
    updated_at: string
    assigned_location: {
      address1: string
      address2: string
      city: string
      country_code: string
      location_id: number
      name: string
      phone: string
      province: string
      zip: string
    }
  }

  //create location interface
}

export default Shopify
