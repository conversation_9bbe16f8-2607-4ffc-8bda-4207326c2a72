import { DateTime } from 'luxon'
import { BaseModel, column } from '@ioc:Adonis/Lucid/Orm'
import { Filterable } from '@ioc:Adonis/Addons/LucidFilter'
import { compose } from '@ioc:Adonis/Core/Helpers'
import SourceFilter from './Filters/SourceFilter'

export default class Source extends compose(BaseModel, Filterable) {
  public static $filter = () => SourceFilter

  @column({ isPrimary: true })
  public id: number

  @column()
  public sourceName: string

  @column()
  public wasDeleted: boolean

  @column()
  public sync: boolean

  @column()
  public secretKey: string

  @column()
  public apiKey: string

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime
}
