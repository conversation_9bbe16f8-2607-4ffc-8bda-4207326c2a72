import { DateTime } from 'luxon'
import { BaseModel, column, computed, HasOne, hasOne } from '@ioc:Adonis/Lucid/Orm'
import AppSetting from './AppSetting'

export default class ShopifySetting extends BaseModel {
  @column({ isPrimary: true })
  public id: number

  @column()
  public settingName: string

  @column()
  public outLocationId: string

  @hasOne(() => AppSetting, {
    foreignKey: 'fulfillmentSettingId',
  })
  public appSetting: HasOne<typeof AppSetting>

  @computed({ serializeAs: 'is_default' })
  public get isDefault() {
    return this.$preloaded.appSetting !== null
  }

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime
}
