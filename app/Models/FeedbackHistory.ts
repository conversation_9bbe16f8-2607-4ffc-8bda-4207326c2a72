import { DateTime } from 'luxon'
import { BaseModel, column, belongsTo, BelongsTo } from '@ioc:Adonis/Lucid/Orm'
import Feedback from './Feedback'
import Admin from './Admin'

export default class FeedbackHistory extends BaseModel {
  @column({ isPrimary: true })
  public id: number

  @column()
  public feedbackId: string

  @belongsTo(() => Feedback, { foreignKey: 'feedbackId' })
  public feedback: BelongsTo<typeof Feedback>

  @column()
  public adminId: number

  @belongsTo(() => Admin, { foreignKey: 'adminId' })
  public admin: BelongsTo<typeof Admin>

  @column()
  public content: string

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime
}
