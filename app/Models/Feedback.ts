import { DateTime } from 'luxon'
import { <PERSON>Model, BelongsTo, belongsTo, column, HasMany, hasMany } from '@ioc:Adonis/Lucid/Orm'
import FeedbackHistory from './FeedbackHistory'
import FeedbackPlatform from './FeedbackPlatform'

export default class Feedback extends BaseModel {
  @column({ isPrimary: true })
  public id: string

  @column()
  public email: string

  @column()
  public firstName: string

  @column()
  public lastName: string

  @column()
  public title: string

  @column()
  public content: string

  @column()
  public token: string

  @column()
  public emailMessageId: string

  @column()
  public isClosed: boolean

  @hasMany(() => FeedbackHistory, {
    foreignKey: 'feedbackId',
  })
  public histories: Has<PERSON><PERSON><typeof FeedbackHistory>

  @column()
  public feedbackPlatformId: string

  @belongsTo(() => FeedbackPlatform, {
    foreignKey: 'feedbackPlatformId',
  })
  public platform: BelongsTo<typeof FeedbackPlatform>

  @column.dateTime()
  public expiresAt: DateTime

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime
}
