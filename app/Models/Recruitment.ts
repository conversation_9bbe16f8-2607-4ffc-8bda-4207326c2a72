import { DateTime } from 'luxon'
import { compose } from '@ioc:Adonis/Core/Helpers'
import { BaseModel, column } from '@ioc:Adonis/Lucid/Orm'
import { Filterable } from '@ioc:Adonis/Addons/LucidFilter'
import RecruitmentFilter from './Filters/RecruitmentFilter'

export default class Recruitment extends compose(BaseModel, Filterable) {
  public static $filter = () => RecruitmentFilter

  @column({ isPrimary: true })
  public id: number

  @column()
  public firstName: string

  @column()
  public lastName: string

  @column()
  public email: string

  @column()
  public message: string

  @column()
  public attachment: string // digital ocean link

  @column()
  public position: string

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime
}
