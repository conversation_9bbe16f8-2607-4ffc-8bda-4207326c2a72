import { DateTime } from 'luxon'
import {
  BaseModel,
  BelongsTo,
  belongsTo,
  column,
  computed,
  Has<PERSON>any,
  hasMany,
} from '@ioc:Adonis/Lucid/Orm'
import Order from './Order'
import Fulfillment from './Fulfillment'
import Event from './Event'
import FulfillmentLineItem from './FulfillmentLineItem'
import ProductVariant from './ProductVariant'

export default class LineItem extends BaseModel {
  @column({ isPrimary: true })
  public id: number

  @column()
  public key: string

  @column()
  public platformLineItemId: string

  // @column()
  // public title: string

  @column()
  public price: number

  @column()
  public linePrice: number

  @column()
  public finalPrice: number

  @column()
  public finalLinePrice: number

  @column()
  public quantity: number

  @column()
  public sku: string

  @belongsTo(() => ProductVariant, {
    serializeAs: 'product_variant',
    foreignKey: 'sku',
    localKey: 'sku',
  })
  public productVariant: BelongsTo<typeof ProductVariant>

  @column()
  public grams: number

  @column()
  public vendor: string

  @column({
    consume: (value: boolean) => Boolean(value),
  })
  public taxable: boolean

  @column({
    prepare: (value: object) => JSON.stringify(value),
    consume: (value: string) => {
      return typeof value != 'string' ? value : JSON.parse(value)
    },
  })
  public properties: object

  @column({
    prepare: (value: object) => JSON.stringify(value),
    consume: (value: string) => {
      return typeof value != 'string' ? value : JSON.parse(value)
    },
  })
  public featuredImage: object

  @column()
  public imageUrl: string

  @column()
  public handle: string

  @column({
    consume: (value: boolean) => Boolean(value),
  })
  public requiresShipping: boolean

  // @column()
  // public productId: number

  // @belongsTo(() => Product)
  // public product: BelongsTo<typeof Product>

  // @column()
  // public variantId: number

  // @belongsTo(() => ProductVariant, {
  //   foreignKey: 'variantId',
  // })
  // public variant: BelongsTo<typeof ProductVariant>

  @column()
  public eventId: number

  @belongsTo(() => Event, {
    foreignKey: 'eventId',
  })
  public event: BelongsTo<typeof Event>

  @column({
    consume: (value: boolean) => Boolean(value),
  })
  public productHasOnlyDefaultVariant: boolean

  @column({
    consume: (value: boolean) => Boolean(value),
  })
  public giftCard: boolean

  // @column()
  // public url: string

  @column()
  public name: string

  @column()
  public productTitle: string

  // @column()
  // public productDescription: string

  // @column()
  // public productType: string

  @column()
  public variantTitle: string

  @column({
    prepare: (value: object) => JSON.stringify(value),
    consume: (value: string) => {
      return typeof value != 'string' ? value : JSON.parse(value)
    },
  })
  public variantOptions: object

  @column({
    prepare: (value: object) => JSON.stringify(value),
    consume: (value: string) => {
      return typeof value != 'string' ? value : JSON.parse(value)
    },
  })
  public optionsWithValues: object

  @hasMany(() => FulfillmentLineItem, {
    foreignKey: 'lineItemId',
    // serializeAs: 'fulfillment_line_items',
    serializeAs: null,
  })
  public fulfillmentLineItems: HasMany<typeof FulfillmentLineItem>

  @computed()
  get fulfillable_quantity() {
    if (!this.$preloaded['fulfillmentLineItems']) return null
    if (
      this.$preloaded['fulfillmentLineItems'][0] &&
      !this.$preloaded['fulfillmentLineItems'][0].$preloaded['event']
    )
      return null
    const totalEventQty = this.fulfillmentLineItems.reduce(
      (prev, current) => prev + current.event.quantity,
      0
    )
    return totalEventQty + this.quantity > 0 ? totalEventQty + this.quantity : 0
  }
  // fulfillable_quantity: The amount available to fulfill, calculated as follows:
  // quantity - max(refunded_quantity, fulfilled_quantity) - pending_fulfilled_quantity - open_fulfilled_quantity

  @column()
  public fulfillmentService: string
  //manual

  //TODO: how to handle this? table control or manual
  @column()
  public fulfillmentStatus: string
  // How far along an order is in terms line items fulfilled.
  // Valid values: null, fulfilled, partial, and not_eligible.

  @column({
    prepare: (value: object) => JSON.stringify(value),
    consume: (value: string) => {
      return typeof value != 'string' ? value : JSON.parse(value)
    },
  })
  public taxLines: object[]

  @column({
    prepare: (value: object) => JSON.stringify(value),
    consume: (value: string) => {
      return typeof value != 'string' ? value : JSON.parse(value)
    },
  })
  public priceSet: object

  @column()
  public totalDiscount: number

  @column({
    prepare: (value: object) => JSON.stringify(value),
    consume: (value: string) => {
      return typeof value != 'string' ? value : JSON.parse(value)
    },
  })
  public totalDiscountSet: object

  @column({
    prepare: (value: object) => JSON.stringify(value),
    consume: (value: string) => {
      return typeof value != 'string' ? value : JSON.parse(value)
    },
  })
  public discountAllocations: object

  @column({
    prepare: (value: object) => JSON.stringify(value),
    consume: (value: string) => {
      return typeof value != 'string' ? value : JSON.parse(value)
    },
  })
  public duties: object

  @column()
  public orderId: number

  @belongsTo(() => Order)
  public order: BelongsTo<typeof Order>

  //TODO: remove this
  @column()
  public fulfillmentId: number

  @belongsTo(() => Fulfillment)
  public fulfillment: BelongsTo<typeof Fulfillment>

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime
}
