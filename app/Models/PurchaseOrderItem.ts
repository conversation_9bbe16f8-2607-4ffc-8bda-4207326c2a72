import { DateTime } from 'luxon'
import { BaseModel, BelongsTo, belongsTo, column } from '@ioc:Adonis/Lucid/Orm'
import ProductVariant from 'App/Models/ProductVariant'
import PurchaseOrder from 'App/Models/PurchaseOrder'

export default class PurchaseOrderItem extends BaseModel {
  @column({ isPrimary: true })
  public id: number

  @column()
  public purchaseOrderId: number

  @belongsTo(() => PurchaseOrder, {
    serializeAs: 'purchase_order',
    foreignKey: 'purchaseOrderId',
  })
  public purchaseOrder: BelongsTo<typeof PurchaseOrder>

  @column()
  public sku: string

  @column()
  public variantId: number

  @column()
  public qty: number

  @belongsTo(() => ProductVariant, {
    serializeAs: 'product_variant',
    foreignKey: 'variantId',
  })
  public productVariant: BelongsTo<typeof ProductVariant>

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime
}
