import { DateTime } from 'luxon'
import { BaseModel, column, computed, HasOne, hasOne } from '@ioc:Adonis/Lucid/Orm'
import AppSetting from './AppSetting'

export default class YamatoSetting extends BaseModel {
  @column({ isPrimary: true })
  public id: number

  @column()
  public settingName: string

  @column()
  public customerNo: string

  @column()
  public unchinNo: string

  @column()
  public kurijouType: string

  @column()
  public timezone: string

  @column({ columnName: 'handling_1', serializeAs: 'handling_1' })
  public handling1: string

  @column({ columnName: 'handling_2', serializeAs: 'handling_2' })
  public handling2: string

  @hasOne(() => AppSetting, {
    foreignKey: 'yamatoSettingId',
  })
  public appSetting: HasOne<typeof AppSetting>

  @computed({ serializeAs: 'is_default' })
  public get isDefault() {
    return this.$preloaded.appSetting !== null
  }

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime
}
