// import ProductVariant from 'App/Models/ProductVariant'
// import Mail from '@ioc:Adonis/Addons/Mail'
// import { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
// import Order from 'App/Models/Order'
// import axios from 'axios'

export default class WebhookOrderFulfillmentsController {
  //fulfillment update webhook
  // public async update({ request }: HttpContextContract) {
  //   const status = request.input('status')
  //   const order = await Order.query()
  //     .where('id', request.input('order_id'))
  //     .preload('customer')
  //     .first()
  //   if (status === 'success' && order) {
  //     const email = request.input('email')
  //     const shopifySkus = request.input('line_items').map((item) => item.sku)
  //     const products = await ProductVariant.query()
  //       .whereIn('shopify_sku', shopifySkus)
  //       .preload('product')
  //     // TODO: setup with ENV
  //     // TODO: maybe store slug in local db
  //     const productInfos = await Promise.all(
  //       await products.map(async (item) => {
  //         const sanityProduct: any = await axios.get(
  //           `https://u7rveea4.api.sanity.io/v1/data/query/production?query=${encodeURIComponent(
  //             `*[_type == 'product' && productID == ${item.product.shopifyId}] { 'slug': slug.current }[0]`
  //           )}`
  //         )
  //         return {
  //           post_url: 'https://casefinite-intl-api.gaincue.com/api/v1/email-reviews',
  //           name: `${item.product.productName} - ${item.variantName}`,
  //           url: `https://en.casefinite.com/products/${sanityProduct.data?.result?.slug}`,
  //         }
  //       })
  //     )
  //     try {
  //       //TODO: fix the reload after submission problem
  //       await Mail.send((message) => {
  //         message
  //           .from(process.env.MAIL_USERNAME as string)
  //           .to(email)
  //           .subject('Thank you for purchasing!')
  //           .htmlView('emails/review_form', {
  //             username: 'benihayashi',
  //             email: email,
  //             order_id: order.id,
  //             products: productInfos,
  //           })
  //       })
  //     } catch (e) {
  //       console.log(e)
  //     }
  //   }
  // }
}
