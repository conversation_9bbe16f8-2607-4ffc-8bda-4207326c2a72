import { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import AppSetting from 'App/Models/AppSetting'

export default class AppSettingsController {
  public async find({ response }: HttpContextContract) {
    const result = await AppSetting.query()
      .preload('fulfillmentSetting')
      .preload('yamatoSetting')
      .preload('shopifySetting')
      .first()
    if (result == null) {
      return response.notFound()
    }

    return response.ok(result)
  }
}
