import { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import axios from 'axios'

export default class YahooApisController {
  //callback for yahoo api to reply with a code, that will be used for getting
  //a new pair of tokens
  public async authCallback({ request }: HttpContextContract) {
    const code = request.input('code')
    console.log(code)
    const appId = process.env.YAHOO_API_KEY!
    const secret = process.env.YAHOO_API_SECRET!

    if (appId !== null && secret !== null) {
      const buf = Buffer.from(`${appId}:${secret}`)
      const access = buf.toString('base64')
      //TODO: complete the parameter
      axios.post(
        'https://auth.login.yahoo.co.jp/yconnect/v1/token',
        {
          code: code,
          grant_type: 'authorization_code',
          redirect_uri:
            'https://7661-2001-d08-d9-deb6-b14d-d61c-4a75-d806.ngrok.io/api/v1/yahoo/token',
        },
        {
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
            'Authorization': access,
          },
        }
      )
    }
  }

  public async tokenCallback({ request }: HttpContextContract) {
    const accessToken = request.input('access_token')
    const refreshToken = request.input('refresh_token')
    console.log('access token : ' + accessToken + 'refresh token : ' + refreshToken)
  }
}
