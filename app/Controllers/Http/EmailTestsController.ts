import Mail from '@ioc:Adonis/Addons/Mail'
import { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import shopifyClient from 'App/Network/shopifyClient'
import axios from 'axios'

export default class EmailTestsController {
  public async send({ request }: HttpContextContract) {
    const email = request.input('email')
    const res = await shopifyClient.get('/checkouts.json')
    const checkouts: any[] = res.data['checkouts']

    checkouts.forEach(async (checkout) => {
      try {
        // TODO: setup with ENV
        // TODO: maybe store slug in local db
        const products = await Promise.all(
          await checkout.line_items.map(async (item) => {
            const sanityProduct: any = await axios.get(
              `https://u7rveea4.api.sanity.io/v1/data/query/production?query=${encodeURIComponent(
                `*[_type == 'product' && productID == ${item.product_id}] { 'slug': slug.current }[0]`
              )}`
            )

            return {
              name: `${item.presentment_title} - ${item.presentment_variant_title}`,
              url: `https://en.casefinite.com/products/${sanityProduct.data?.result?.slug}`,
              quantity: item.quantity,
              total: item.price * item.quantity,
            }
          })
        )

        //TODO: fix the reload after submission problem
        await Mail.send((message) => {
          message
            .from(process.env.MAIL_USERNAME as string)
            .to(email)
            .subject("Don't Miss Out!")
            .htmlView('emails/abandoned_checkout_recovery', {
              username: checkout.customer.firstName + ' ' + checkout.customer.lastName,
              email: email,
              products: products,
              currency: checkout.presentment_currency,
              checkout_url: checkout.abandoned_checkout_url,
            })
        })
      } catch (e) {
        console.log(e)
      }
    })
  }

  public async review({ request, response }: HttpContextContract) {
    console.log(request.all())

    return response.send({ message: 'success' })
  }
}
