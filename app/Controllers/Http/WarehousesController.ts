import { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import { schema } from '@ioc:Adonis/Core/Validator'
import Database from '@ioc:Adonis/Lucid/Database'
import Warehouse from 'App/Models/Warehouse'

export default class WarehousesController {
  public async find({ request, response }: HttpContextContract) {
    const page = request.input('page', 1)
    const limit = request.input('limit', 20)
    const sort = request.input('sort', 'id:desc').split(':')

    const warehouses = await Warehouse.filter(request.all())
      .orderBy(sort[0], sort[1])
      .paginate(page, limit)
    response.ok(warehouses)
  }

  public async findOne({ response, params }: HttpContextContract) {
    try {
      const warehouse = await Warehouse.find(params.id)
      if (warehouse == null) {
        return response.notFound()
      } else {
        return response.ok(warehouse)
      }
    } catch (e) {
      response.internalServerError(e)
    }
  }

  public async getByDefault({ response }: HttpContextContract) {
    try {
      const warehouse = await Database.query()
        .from('warehouses')
        .where((query) => {
          query.where('is_default', true)
        })
      if (warehouse == null) {
        return response.notFound()
      } else {
        return response.ok(warehouse)
      }
    } catch (e) {
      response.internalServerError(e)
    }
  }

  public async create({ request, response }: HttpContextContract) {
    const warehouseSchema = schema.create({
      warehouse_name: schema.string({ trim: true }),
      location: schema.string({ trim: true }),
      address: schema.string({ trim: true }),
      is_default: schema.boolean.optional(),
    })

    const body = await request.validate({ schema: warehouseSchema })
    const warehouse = await Warehouse.create(body)

    response.created(warehouse)
  }

  public async update({ request, response, params }: HttpContextContract) {
    const postSchema = schema.create({
      warehouse_name: schema.string.optional({ trim: true }),
      location: schema.string.optional({ trim: true }),
      addess: schema.string.optional({ trim: true }),
      is_default: schema.boolean.optional(),
    })
    try {
      const payload = await request.validate({ schema: postSchema })
      const warehouse = await Warehouse.find(params.id)

      if (warehouse != null) {
        warehouse.merge(payload)
        await warehouse.save()
        return response.ok(warehouse)
      }
      return response.notFound()
    } catch (e) {
      return response.internalServerError(e)
    }
  }

  public async delete({ response, params: { id } }: HttpContextContract) {
    const warehouse = await Warehouse.find(id)
    if (warehouse != null) {
      await warehouse.delete()
      return response.ok(warehouse)
    }
    return response.notFound()
  }
}
