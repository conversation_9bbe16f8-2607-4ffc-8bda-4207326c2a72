import KuromojiAnalyzer from 'kuroshiro-analyzer-kuromoji'
import { Exception } from '@adonisjs/core/build/standalone'
import { schema, rules } from '@ioc:Adonis/Core/Validator'
import { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import Order from 'App/Models/Order'
import ObjectToCsv from 'node-create-csv'
import Fs from 'fs'
import shopifyClient from 'App/Network/shopifyClient'
import TransactionStatus from 'App/Models/TransactionStatus'
import FulfillmentStatus from 'App/Models/FulfillmentStatus'
import Database from '@ioc:Adonis/Lucid/Database'
import <PERSON><PERSON><PERSON> from 'kuroshiro'
import _ from 'lodash'
import {
  checkClosedOrders,
  fulfillShopifyOrder,
  importOrdersFromShopify,
} from 'App/Modules/ShopifyOrderServices'
import AppSetting from 'App/Models/AppSetting'
import { archiveFiles } from '../../Modules/ArchiveFiles'
import { AxiosResponse } from 'axios'
import OrderStatus from 'App/Models/OrderStatus'
import Blacklist from 'App/Models/Blacklist'
import { YamatoCSVTracking } from 'App/Modules/YamatoOrderService'
import { SagawaCSVTracking } from 'App/Modules/SagawaOrderService'
import { SagawaPostCSVTracking } from 'App/Modules/SagawaPostOrderService'

export default class OrderCSVController {
  public async find({ request, response }: HttpContextContract) {
    const page = request.input('page', 1)
    const limit = request.input('limit', 20)
    const sort = request.input('sort', 'created_at:desc').split(':')
    const result = await Order.filter(request.all())
      .preload('customer')
      .preload('transactionStatus')
      .preload('status')
      .preload('shopPlatform')
      .preload('lineItems', (query) => {
        query.preload('productVariant')
        query.preload('fulfillmentLineItems', (query) => {
          query.preload('event')
        })
      })
      .preload('fulfillments', (query) => {
        query.preload('fulfillmentLineItems', (query) => {
          query.preload('lineItem')
          query.preload('event')
        })
      })
      .leftJoin('customers', 'orders.customer_id', 'customers.id')
      .leftJoin('line_items', 'line_items.order_id', 'orders.id')
      .leftJoin('fulfillments', 'fulfillments.order_id', 'orders.id')
      .select('orders.*')
      .select('customers.name as customer_name')
      .groupBy('orders.id')
      .orderBy(sort[0], sort[1])
      .paginate(page, limit)

    return response.ok(result)
  }

  public async findByTracking({ request, response }: HttpContextContract) {
    const trackingNumber = request.input('tracking_number')
    const trackingCompanyNames = request.input('tracking_company_names').split(',')
    if (!trackingNumber) {
      return response.badRequest({ message: 'tracking_number not specified' })
    }

    const order = await Order.query()
      .join('fulfillments', 'fulfillments.order_id', '=', 'orders.id')
      .where('fulfillments.tracking_number', trackingNumber)
      .andWhereIn('fulfillments.tracking_company_name', trackingCompanyNames)
      .preload('customer')
      .preload('transactionStatus')
      .preload('fulfillmentStatus')
      .preload('shopPlatform')
      .preload('lineItems', (query) => {
        query.preload('productVariant')
        query.preload('fulfillmentLineItems', (query) => {
          query.preload('event')
        })
      })
      .preload('fulfillments', (query) => {
        query.preload('fulfillmentLineItems', (query) => {
          query.preload('lineItem')
          query.preload('event')
        })
      })
      .first()

    return response.ok(order)
  }

  public async findOne({ response, params: { id } }: HttpContextContract) {
    const result = await Order.query()
      .where('id', id)
      .preload('customer')
      .preload('transactionStatus')
      .preload('fulfillmentStatus')
      .preload('shopPlatform')
      .preload('lineItems', (query) => {
        query.preload('productVariant')
        query.preload('fulfillmentLineItems', (query) => {
          query.preload('event')
        })
      })
      .preload('fulfillments', (query) => {
        query.preload('fulfillmentLineItems', (query) => {
          query.preload('lineItem')
          query.preload('event')
        })
      })
      .first()
    if (result == null) {
      return response.notFound()
    }

    return response.ok(result)
  }

  public async getRevenue({ request, response }: HttpContextContract) {
    const dateNow = new Date()
    const endDate = request.input(
      'end',
      `${dateNow.getFullYear()}-${dateNow.getMonth() + 1}-${dateNow.getDate()}`
    )
    dateNow.setDate(dateNow.getDate() - 1)
    const startDate = request.input(
      'start',
      `${dateNow.getFullYear()}-${dateNow.getMonth() + 1}-${dateNow.getDate()}`
    )
    console.log(startDate, endDate)

    //TODO: should use maybe transaction date?
    const result = await Order.query()
      .whereBetween('orders.created_at', [startDate, endDate])
      .andWhere('orders.transaction_status_id', 3)
      .join('line_items', 'line_items.order_id', '=', 'orders.id')
      .sum('line_items.price as revenue')
      .count('* as total_order')
      .first()

    return response.ok({
      total_revenue: result?.$extras.revenue,
      total_order: result?.$extras.total_order,
    })
  }

  public async getTransactionTypes({ response }: HttpContextContract) {
    const result = await TransactionStatus.all()
    return response.ok(result)
  }

  public async getFulfillmentTypes({ response }: HttpContextContract) {
    const result = await FulfillmentStatus.all()
    return response.ok(result)
  }

  public async getOrderTypes({ response }: HttpContextContract) {
    const result = await OrderStatus.all()
    return response.ok(result)
  }

  //TODO: no longer needed
  public async getOrderProduct({ response, params: { id } }: HttpContextContract) {
    const order = await Order.find(id)
    const event = await order
      ?.related('event')
      .query()
      .preload('stock', (query) => {
        query.preload('variant', (query) => {
          query.preload('product')
        })
      })
    if (event == null) {
      return response.notFound({ message: 'No such event' })
    }
    const orderProducts: any[] = []

    for (var i = 0; i < event.length; i++) {
      const product = {
        ...event[i].stock.variant.$attributes,
        id: event[i].stock.variant.id,
        name: event[i].stock.variant.fullname,
        quantity: event[i].quantity,
      }
      orderProducts.push(product)
    }

    return response.ok(orderProducts)
  }

  public async updateStatus({ request, response, params: { id } }: HttpContextContract) {
    const postSchema = schema.create({
      order_status_id: schema.number([
        rules.exists({
          table: 'order_statuses',
          column: 'id',
        }),
      ]),
    })
    try {
      const payload = await request.validate({ schema: postSchema })
      const order = await Order.find(id)

      if (order == null) {
        return response.notFound()
      }

      if (order.orderStatusId === 5) {
        return response.badRequest({ message: 'this order has already been fulfilled' })
      }

      if (order.orderStatusId === 6) {
        return response.badRequest({ message: 'this order was partially shipped' })
      }

      if (order.orderStatusId === 7) {
        return response.badRequest({ message: 'this order was cancelled' })
      }

      order.orderStatusId = payload.order_status_id
      await order.save()
      return response.ok({ success: true, data: order })
    } catch (e) {
      return response.internalServerError(e)
    }
  }

  public async updateOperatorMemo({ request, response, params: { id } }: HttpContextContract) {
    try {
      const order = await Order.find(id)
      if (order == null) {
        return response.notFound()
      }

      const body = request.body()
      order.operatorMemo = body.operator_memo
      await order.save()

      return response.ok(order)
    } catch (e) {
      return response.internalServerError(e)
    }
  }

  public async getYamatoCSV({ request, response }: HttpContextContract) {
    //before actually creating the csv, import all active orders from shopify first,
    //to ensure everything is up to date

    const postSchema = schema.create({
      ignore_generated: schema.boolean.optional(),
      waiting_shipped_only: schema.boolean.optional(),
      generate_picking_list: schema.boolean.optional(),
      separate_order_limit: schema.number.optional([rules.range(1, 999)]),
      remove_jp_phone_code: schema.boolean.optional(),
      default_item_name: schema.string.optional({ trim: true }),
      ids: schema.array.optional().members(schema.number()),
      sort_by: schema.string.optional({}, [
        rules.inValues(['id', 'product_name', 'sku', 'platform_order_id', 'name', 'created_at']),
      ]),
      desc: schema.boolean.optional(),
      id_column: schema.string.optional({}, [rules.inValues(['id', 'platform_order_id', 'name'])]),
      exclude_sku: schema.array.optional().members(schema.string()),
      full_exclude_sku: schema.boolean.optional(),
      include_sku: schema.array.optional().members(schema.string()),
      full_include_sku: schema.boolean.optional(),
    })

    try {
      const payload = await request.validate({ schema: postSchema })

      const kuroshiro = new Kuroshiro()
      await kuroshiro.init(new KuromojiAnalyzer())

      let orderQuery = Order.query()
      .join('line_items', 'line_items.order_id', '=', 'orders.id')

      if (payload.waiting_shipped_only) {
        orderQuery = orderQuery
          .andWhere('orders.order_status_id', 4)
          .andWhereIn('orders.transaction_status_id', [1, 3])
      }

      if (payload.ignore_generated) {
        orderQuery = orderQuery.andWhere('orders.generated', false)
      }

      if (payload.ids && payload.ids.length > 0) {
        orderQuery = orderQuery.andWhereIn('orders.id', payload.ids)
      }

      //exclude certain sku
      if (
        payload.exclude_sku &&
        payload.exclude_sku.length > 0 &&
        payload.full_exclude_sku == true
      ) {
        // if (payload.full_exclude_sku == true) {
        //   const excludedOrders = await filterQuery
        //     .join('line_items', 'line_items.order_id', '=', 'orders.id')
        //     .groupBy('orders.id')
        //     .select('orders.*')
        //     .whereIn('line_items.sku', payload.exclude_sku)

        //   orderQuery = orderQuery.whereNotIn(
        //     'orders.id',
        //     excludedOrders.map((item) => item.id)
        //   )
        // }

        orderQuery = orderQuery.whereNotIn('line_items.sku', payload.exclude_sku)
      }

      //include certain sku
      if (
        payload.include_sku &&
        payload.include_sku.length > 0 &&
        payload.full_include_sku == true
      ) {
        orderQuery = orderQuery.whereIn('line_items.sku', payload.include_sku)
      }

      //for product name and sku will be do in another way, since it involves aggregation
      if (
        payload.sort_by === 'id' ||
        payload.sort_by === 'platform_order_id' ||
        payload.sort_by === 'name' ||
        payload.sort_by === 'created_at'
      ) {
        const direction = payload.desc ? 'desc' : 'asc'
        orderQuery = orderQuery.orderBy(`orders.${payload.sort_by}`, direction)
      }

      var orders: Order[]
      const appSetting = await AppSetting.query()
        .preload('fulfillmentSetting')
        .preload('yamatoSetting')
        .first()
      const yamatoSetting = appSetting?.yamatoSetting
      const fulfillmentSetting = appSetting?.fulfillmentSetting
      if (!yamatoSetting) {
        return response.internalServerError({
          message: 'Yamato settings could not be found',
        })
      }
      if (!fulfillmentSetting) {
        return response.internalServerError({
          message: 'Fulfillment settings could not be found',
        })
      }
      const filepaths: string[] = []
      const dataCmb: any[] = []
      const utc0Date = new Date()
      const date = new Date(utc0Date.getTime() + 9 * 60 * 60 * 1000)
      const dateArr = date.toISOString().split('T')[0].split('-')
      const dateStr = `${dateArr[0]}-${dateArr[1]}-${dateArr[2]}`

      orders = await orderQuery
        .preload('customer')
        .preload('lineItems', (query) => {
          query.preload('fulfillmentLineItems', (query) => {
            query.preload('event')
          })
        })
        .groupBy('orders.id')
        .select('orders.*')

      if (orders.length < 1) {
        return response.notFound({ message: 'No records were found' })
      }

      if (payload.generate_picking_list) {
        const pickingListFilePaths = await createOrderPickingList(
          // payload.separate_order_limit,
          payload.waiting_shipped_only,
          payload.ignore_generated,
          orders.map((item) => item.id)
        )
        filepaths.push(...pickingListFilePaths)
      }

      const idColumn = payload.id_column ? _.camelCase(payload.id_column) : 'id'
      //TODO: need to revise the efficiency
      if (payload.sort_by === 'product_name') {
        orders = orders.sort((a, b) => {
          if (!a.lineItems[0] || !b.lineItems[0]) {
            return 0
          }
          const fullnameA = `${a.lineItems[0]?.productTitle} ${a.lineItems[0]?.variantTitle}`
          const fullnameB = `${b.lineItems[0]?.productTitle} ${b.lineItems[0]?.variantTitle}`
          if (payload.desc) {
            return fullnameA.localeCompare(fullnameB)
          }
          return fullnameB.localeCompare(fullnameA)
        })
      }

      if (payload.sort_by === 'sku') {
        orders = orders.sort((a, b) => {
          if (!a.lineItems[0] || !b.lineItems[0]) {
            return 0
          }
          const skuA = a.lineItems[0]?.sku
          const skuB = b.lineItems[0]?.sku
          if (!skuA || !skuB) {
            return 0
          }
          if (payload.desc) {
            return skuA.localeCompare(skuB)
          }
          return skuB.localeCompare(skuA)
        })
      }

      for (var i = 0; i < orders.length; i++) {
        const order = orders[i]
        const customer = orders[i].customer
        const fulfillableLineItem = order.lineItems.filter(
          (liteItem) => liteItem.fulfillable_quantity && liteItem.fulfillable_quantity > 0
        )
        const customerName =
          orders[i].firstName || orders[i].lastName
            ? `${orders[i].lastName} ${orders[i].firstName}`
            : customer.name
        const phone = order.phone ?? customer.phone ?? '1'
        var formattedPhone = phone
        if (phone?.length > 0 && !payload.remove_jp_phone_code) {
          formattedPhone = `+81 ${phone.slice(phone[0] == '0' ? 1 : 0, phone.length)}`
        }
        const prodname1 =
          fulfillableLineItem.length > 0
            ? `${fulfillableLineItem[0]?.productTitle} - ${fulfillableLineItem[0]?.variantTitle}`
            : ''
        const prodname2 =
          fulfillableLineItem.length > 1
            ? `${fulfillableLineItem[1]?.productTitle} - ${fulfillableLineItem[1]?.variantTitle}`
            : ''

        const hankakuAddress = `${fulfillmentSetting.province}${fulfillmentSetting.city}${
          fulfillmentSetting.address1 ?? ''
        }`.replace('　', ' ')

        var data = {
          'お客様管理番号': `${order[idColumn]}`, //`${order.order}${filteredOrders.length > 1 ? '-' + (j + 1) : ''}`,
          '送り状種別': yamatoSetting.kurijouType,
          '温度区分': '',
          '予備4': '',
          //TODO: not sure what to put here
          '出荷予定日': `${dateArr[0]}/${dateArr[1]}/${dateArr[2]}`,
          '配達指定日': '',
          '配達時間帯区分': yamatoSetting.timezone ?? '',
          '届け先コード': '',
          '届け先電話番号': payload.remove_jp_phone_code
            ? formattedPhone.replace('+81', '')
            : formattedPhone, //order.customer.phone
          '届け先電話番号(枝番)': '',
          '届け先郵便番号': `${order.zip}`, //order.billing_address.zip
          '届け先住所': `${order.address.replace('　', '')}`, //order.billing_address.address1 + order.billing_address.address2
          'お届け先建物名（ｱﾊﾟｰﾄﾏﾝｼｮﾝ名）': order.apartment ?? '',
          '会社・部門名１': order.company,
          '会社・部門名２': '',
          '届け先名(漢字)': customerName, //order.billing_address.last_name + order.billing_address.first_name
          '届け先名(カナ)': '',
          // jconv.toHanKana(
          //   await kuroshiro.convert(customer.name, { to: 'katakana' })
          // ), //katakana version of name
          '敬称': '',
          '依頼主コード': '',
          '依頼主電話番号': `${fulfillmentSetting.phone}`,
          '依頼主電話番号(枝番)': '',
          '依頼主郵便番号': fulfillmentSetting.postal,
          '依頼主住所': hankakuAddress,
          '依頼主建物名（ｱﾊﾟｰﾄﾏﾝｼｮﾝ名）': fulfillmentSetting.address2 ?? '',
          '依頼主名（漢字）': fulfillmentSetting.name,
          '依頼主名(カナ)': '',
          '品名コード１': fulfillableLineItem.length > 0 ? fulfillableLineItem[0].sku ?? '' : '',
          '品名１': prodname1.length <= 20 ? prodname1 : payload.default_item_name,
          '品名コード２': fulfillableLineItem.length > 1 ? fulfillableLineItem[1].sku ?? '' : '',
          '品名2': prodname2.length <= 20 ? prodname2 : payload.default_item_name,
          '荷扱い１': yamatoSetting.handling1,
          '荷扱い２': yamatoSetting.handling2,
          '記事': '',
          'コレクト代金引換額(税込)': '',
          'コレクト内消費税額': '',
          '営業所止置き': '',
          '止め置き営業所コード': '',
          '発行枚数': '',
          '個数口枠の印字': '',
          '請求先顧客コード': `${yamatoSetting?.customerNo}`,
          '請求先分類コード': '',
          //default to 01 if the user did not set other values at yamato
          '運賃管理番号': `${yamatoSetting?.unchinNo ?? '01'}`,
        }

        dataCmb.push(data)
      }

      await new ObjectToCsv(dataCmb).toDisk(`${process.env.CSV_DIR}/yamato-${dateStr}.csv`, {
        showHeader: true,
      }) //save to disk
      filepaths.push(`yamato-${dateStr}.csv`)
      const compressedFilePath = await archiveFiles(filepaths, dateStr)

      response.stream(Fs.createReadStream(compressedFilePath))
      // } catch (e) {
      //   console.log(e)
      //   return response.badRequest(e)
      // }

      // response.download(`./tmp/generatedCSV/${dateStr}.rar`)
      await Order.query()
        .whereIn(
          'id',
          orders.map((item) => item.id)
        )
        .update({ generated: true })
    } catch (e) {
      console.log(e)
      return response.badRequest({ message: e })
    }
  }

  public async getSagawaCSV({ request, response }: HttpContextContract) {
    //before actually creating the csv, import all active orders from shopify first,
    //to ensure everything is up to date

    const postSchema = schema.create({
      ignore_generated: schema.boolean.optional(),
      waiting_shipped_only: schema.boolean.optional(),
      generate_picking_list: schema.boolean.optional(),
      separate_order_limit: schema.number.optional([rules.range(1, 999)]),
      remove_jp_phone_code: schema.boolean.optional(),
      default_item_name: schema.string.optional({ trim: true }),
      ids: schema.array.optional().members(schema.number()),
      sort_by: schema.string.optional({}, [
        rules.inValues(['id', 'product_name', 'sku', 'platform_order_id', 'name', 'created_at']),
      ]),
      desc: schema.boolean.optional(),
      id_column: schema.string.optional({}, [rules.inValues(['id', 'platform_order_id', 'name'])]),
      exclude_sku: schema.array.optional().members(schema.string()),
      full_exclude_sku: schema.boolean.optional(),
      include_sku: schema.array.optional().members(schema.string()),
      full_include_sku: schema.boolean.optional(),
    })

    try {
      const payload = await request.validate({ schema: postSchema })

      const kuroshiro = new Kuroshiro()
      await kuroshiro.init(new KuromojiAnalyzer())

      let orderQuery = Order.query()

      if (payload.waiting_shipped_only) {
        orderQuery = orderQuery
          .andWhere('orders.order_status_id', 4)
          .andWhereIn('orders.transaction_status_id', [1, 3])
      }

      if (payload.ignore_generated) {
        orderQuery = orderQuery.andWhere('orders.generated', false)
      }

      if (payload.ids && payload.ids.length > 0) {
        orderQuery = orderQuery.andWhereIn('orders.id', payload.ids)
      }

      //exclude certain sku
      if (
        payload.exclude_sku &&
        payload.exclude_sku.length > 0 &&
        payload.full_exclude_sku == true
      ) {
        // if (payload.full_exclude_sku == true) {
        //   const excludedOrders = await filterQuery
        //     .join('line_items', 'line_items.order_id', '=', 'orders.id')
        //     .groupBy('orders.id')
        //     .select('orders.*')
        //     .whereIn('line_items.sku', payload.exclude_sku)

        //   orderQuery = orderQuery.whereNotIn(
        //     'orders.id',
        //     excludedOrders.map((item) => item.id)
        //   )
        // }

        orderQuery = orderQuery.whereNotIn('line_items.sku', payload.exclude_sku)
      }

      //include certain sku
      if (
        payload.include_sku &&
        payload.include_sku.length > 0 &&
        payload.full_include_sku == true
      ) {
        orderQuery = orderQuery.whereIn('line_items.sku', payload.include_sku)
      }

      //for product name and sku will be do in another way, since it involves aggregation
      if (
        payload.sort_by === 'id' ||
        payload.sort_by === 'platform_order_id' ||
        payload.sort_by === 'name' ||
        payload.sort_by === 'created_at'
      ) {
        const direction = payload.desc ? 'desc' : 'asc'
        orderQuery = orderQuery.orderBy(`orders.${payload.sort_by}`, direction)
      }

      var orders: Order[]
      const appSetting = await AppSetting.query()
        .preload('fulfillmentSetting')
        .preload('yamatoSetting')
        .first()
      const yamatoSetting = appSetting?.yamatoSetting
      const fulfillmentSetting = appSetting?.fulfillmentSetting
      if (!yamatoSetting) {
        return response.internalServerError({
          message: 'Yamato settings could not be found',
        })
      }
      if (!fulfillmentSetting) {
        return response.internalServerError({
          message: 'Fulfillment settings could not be found',
        })
      }
      const filepaths: string[] = []
      const dataCmb: any[] = []
      const utc0Date = new Date()
      const date = new Date(utc0Date.getTime() + 9 * 60 * 60 * 1000)
      const dateArr = date.toISOString().split('T')[0].split('-')
      const dateStr = `${dateArr[0]}-${dateArr[1]}-${dateArr[2]}`

      orders = await orderQuery
        .join('line_items', 'line_items.order_id', '=', 'orders.id')
        .preload('customer')
        .preload('lineItems', (query) => {
          query.preload('fulfillmentLineItems', (query) => {
            query.preload('event')
          })
        })
        .groupBy('orders.id')
        .select('orders.*')

      if (orders.length < 1) {
        return response.notFound({ message: 'No records were found' })
      }

      if (payload.generate_picking_list) {
        const pickingListFilePaths = await createOrderPickingList(
          // payload.separate_order_limit,
          payload.waiting_shipped_only,
          payload.ignore_generated,
          orders.map((item) => item.id)
        )
        filepaths.push(...pickingListFilePaths)
      }

      const idColumn = payload.id_column ? _.camelCase(payload.id_column) : 'id'
      //TODO: need to revise the efficiency
      if (payload.sort_by === 'product_name') {
        orders = orders.sort((a, b) => {
          if (!a.lineItems[0] || !b.lineItems[0]) {
            return 0
          }
          const fullnameA = `${a.lineItems[0]?.productTitle} ${a.lineItems[0]?.variantTitle}`
          const fullnameB = `${b.lineItems[0]?.productTitle} ${b.lineItems[0]?.variantTitle}`
          if (payload.desc) {
            return fullnameA.localeCompare(fullnameB)
          }
          return fullnameB.localeCompare(fullnameA)
        })
      }

      if (payload.sort_by === 'sku') {
        orders = orders.sort((a, b) => {
          if (!a.lineItems[0] || !b.lineItems[0]) {
            return 0
          }
          const skuA = a.lineItems[0]?.sku
          const skuB = b.lineItems[0]?.sku
          if (!skuA || !skuB) {
            return 0
          }
          if (payload.desc) {
            return skuA.localeCompare(skuB)
          }
          return skuB.localeCompare(skuA)
        })
      }

      for (var i = 0; i < orders.length; i++) {
        const order = orders[i]
        const customer = orders[i].customer
        const fulfillableLineItem = order.lineItems.filter(
          (liteItem) => liteItem.fulfillable_quantity && liteItem.fulfillable_quantity > 0
        )
        const customerName =
          orders[i].firstName || orders[i].lastName
            ? `${orders[i].lastName} ${orders[i].firstName}`
            : customer.name
        const phone = order.phone ?? customer.phone ?? '1'
        var formattedPhone = phone
        if (phone?.length > 0 && !payload.remove_jp_phone_code) {
          formattedPhone = `+81 ${phone.slice(phone[0] == '0' ? 1 : 0, phone.length)}`
        }
        const prodname1 =
          fulfillableLineItem.length > 0
            ? `${fulfillableLineItem[0]?.productTitle} - ${fulfillableLineItem[0]?.variantTitle}`
            : ''
        const prodname2 =
          fulfillableLineItem.length > 1
            ? `${fulfillableLineItem[1]?.productTitle} - ${fulfillableLineItem[1]?.variantTitle}`
            : ''

        const hankakuAddress = `${fulfillmentSetting.province}${fulfillmentSetting.city}${
          fulfillmentSetting.address1 ?? ''
        }`.replace('　', ' ')

        var data = {
          'お客様管理番号': `${order[idColumn]}`, //`${order.order}${filteredOrders.length > 1 ? '-' + (j + 1) : ''}`,
          '送り状種別': yamatoSetting.kurijouType,
          '温度区分': '',
          '予備4': '',
          '出荷予定日': `${dateArr[0]}/${dateArr[1]}/${dateArr[2]}`,
          '配達指定日': '',
          '配達時間帯区分': yamatoSetting.timezone ?? '',
          '届け先コード': '',
          '届け先電話番号': payload.remove_jp_phone_code
            ? formattedPhone.replace('+81', '')
            : formattedPhone, //order.customer.phone
          '届け先電話番号(枝番)': '',
          '届け先郵便番号': `${order.zip}`, //order.billing_address.zip
          '届け先住所': `${order.address.replace('　', ' ')}`, //order.billing_address.address1 + order.billing_address.address2
          'お届け先建物名（ｱﾊﾟｰﾄﾏﾝｼｮﾝ名）': order.apartment ?? '',
          '会社・部門名１': order.company,
          '会社・部門名２': '',
          '届け先名(漢字)': customerName, //order.billing_address.last_name + order.billing_address.first_name
          '届け先名(カナ)': '',
          // jconv.toHanKana(
          //   await kuroshiro.convert(customer.name, { to: 'katakana' })
          // ), //katakana version of name
          '敬称': '',
          '依頼主コード': '',
          '依頼主電話番号': `${fulfillmentSetting.phone}`,
          '依頼主電話番号(枝番)': '',
          '依頼主郵便番号': fulfillmentSetting.postal,
          '依頼主住所': hankakuAddress,
          '依頼主建物名（ｱﾊﾟｰﾄﾏﾝｼｮﾝ名）': fulfillmentSetting.address2 ?? '',
          '依頼主名（漢字）': fulfillmentSetting.name,
          '依頼主名(カナ)': '',
          '品名コード１': fulfillableLineItem.length > 0 ? fulfillableLineItem[0].sku ?? '' : '',
          '品名１': prodname1.length <= 20 ? prodname1 : payload.default_item_name,
          '品名コード２': fulfillableLineItem.length > 1 ? fulfillableLineItem[1].sku ?? '' : '',
          '品名2': prodname2.length <= 20 ? prodname2 : payload.default_item_name,
          '荷扱い１': yamatoSetting.handling1,
          '荷扱い２': yamatoSetting.handling2,
          '記事': '',
          'コレクト代金引換額(税込)': '',
          'コレクト内消費税額': '',
          '営業所止置き': '',
          '止め置き営業所コード': '',
          '発行枚数': '',
          '個数口枠の印字': '',
          '請求先顧客コード': `${yamatoSetting?.customerNo}`,
          '請求先分類コード': '',
          //default to 01 if the user did not set other values at yamato
          '運賃管理番号': `${yamatoSetting?.unchinNo ?? '01'}`,
        }

        dataCmb.push(data)
      }

      await new ObjectToCsv(dataCmb).toDisk(`${process.env.CSV_DIR}/yamato-${dateStr}.csv`, {
        showHeader: true,
      }) //save to disk
      filepaths.push(`yamato-${dateStr}.csv`)
      const compressedFilePath = await archiveFiles(filepaths, dateStr)

      response.stream(Fs.createReadStream(compressedFilePath))
      // } catch (e) {
      //   console.log(e)
      //   return response.badRequest(e)
      // }

      // response.download(`./tmp/generatedCSV/${dateStr}.rar`)
      await Order.query()
        .whereIn(
          'id',
          orders.map((item) => item.id)
        )
        .update({ generated: true })
    } catch (e) {
      console.log(e)
      return response.badRequest({ message: e })
    }
  }

  public async getPickingList({ request, response }: HttpContextContract) {
    const postSchema = schema.create({
      separate_order_limit: schema.number.optional(),
      waiting_shipped_only: schema.boolean.optional(),
      ignore_generated: schema.boolean.optional(),
    })

    try {
      const payload = await request.validate({ schema: postSchema })
      const filepaths = await createOrderPickingList(
        // payload.separate_order_limit,
        payload.waiting_shipped_only,
        payload.ignore_generated
      )
      const date = new Date()
      const dateArr = date.toISOString().split('T')[0].split('-')
      const dateStr = `${dateArr[0]}-${dateArr[1]}-${dateArr[2]}`
      const archivePath = await archiveFiles(filepaths, dateStr)
      response.stream(Fs.createReadStream(archivePath))
    } catch (e) {
      return response.badRequest(e)
    }
  }

  public async updateTrackingYamato({ request, response }: HttpContextContract) {
    const postSchema = schema.create({
      id_column: schema.string.optional({}, [rules.inValues(['id', 'platform_order_id', 'name'])]),
      yamato_csv: schema.file({
        extnames: ['csv', 'xlsx'],
      }),
    })

    try {
      const payload = await request.validate({ schema: postSchema })
      const file = payload.yamato_csv

      if (file.tmpPath != null) {
        const filePath: string = file.tmpPath
        const results = await YamatoCSVTracking(
          filePath,
          payload.id_column
          // payload.update_to_shipped,
          // payload.notify_customer
        )
        return response.created({ data: results, success: true })
      }
      return response.badRequest({ error: 'missing csv' })
    } catch (e) {
      console.log(e)
      return response.badRequest(e)
    }
  }

  public async updateTrackingSagawa({ request, response }: HttpContextContract) {
    const postSchema = schema.create({
      id_column: schema.string.optional({}, [rules.inValues(['id', 'platform_order_id', 'name'])]),
      sagawa_csv: schema.file({
        extnames: ['csv', 'xlsx'],
      }),
    })

    try {
      const payload = await request.validate({ schema: postSchema })
      const file = payload.sagawa_csv

      if (file.tmpPath != null) {
        const filePath: string = file.tmpPath
        const results = await SagawaCSVTracking(
          filePath,
          payload.id_column
          // payload.update_to_shipped,
          // payload.notify_customer
        )
        return response.created({ data: results, success: true })
      }
      return response.badRequest({ error: 'missing csv' })
    } catch (e) {
      console.log(e)
      return response.badRequest(e)
    }
  }

  public async updateTrackingSagawaPost({ request, response }: HttpContextContract) {
    const postSchema = schema.create({
      id_column: schema.string.optional({}, [rules.inValues(['id', 'platform_order_id', 'name'])]),
      sagawa_csv: schema.file({
        extnames: ['csv', 'xlsx'],
      }),
    })

    try {
      const payload = await request.validate({ schema: postSchema })
      const file = payload.sagawa_csv

      if (file.tmpPath != null) {
        const filePath: string = file.tmpPath
        const results = await SagawaPostCSVTracking(
          filePath,
          payload.id_column
          // payload.update_to_shipped,
          // payload.notify_customer
        )
        return response.created({ data: results, success: true })
      }
      return response.badRequest({ error: 'missing csv' })
    } catch (e) {
      console.log(e)
      return response.badRequest(e)
    }
  }

  public async orderFulfillment({ request, response, params: { id } }: HttpContextContract) {
    //TODO: change fulfillment in to one to many relationship
    const postSchema = schema.create({
      notify_customer: schema.boolean.optional(),
    })
    const order = await Order.query()
      .where('id', id)
      .preload('lineItems')
      .preload('fulfillments')
      .first()
    if (order == null) {
      return response.notFound()
    }

    //check if order is eligible for direct fulfillment
    if (order.orderStatusId === 7) {
      return response.unprocessableEntity({ message: 'this order was cancelled' })
    }

    //check if order is eligible for direct fulfillment
    // if (order.orderStatusId === 6) {
    //   return response.unprocessableEntity({ message: 'this order was partially shipped' })
    // }

    //sync order here to double confirm with the order status
    // try {
    //   const res = await shopifyClient.get(`/orders/${order.order}.json`)
    //   const shopifyOrder = res.data['order']

    // } catch (e) {
    //   console.log('import order from order fulfillment ', e)
    // }

    //check if there are any pending fulfillments
    const pendingFulfillment = order.fulfillments.find((item) => item.fulfillmentStatusId === 1)

    if (!pendingFulfillment) {
      return response.internalServerError({ message: 'this order does not have a tracking code' })
    }

    try {
      const payload = await request.validate({ schema: postSchema })
      const findFulfillments = await fulfillShopifyOrder(
        pendingFulfillment.id,
        undefined,
        payload.notify_customer
      )

      return response.ok({ data: findFulfillments, success: true })
    } catch (e) {
      console.log('order fufillment', e)
      return response.badRequest({ message: e.message })
    }
  }

  public async orderCapture({ response, params: { id } }: HttpContextContract) {
    try {
      const order = await Order.find(id)
      if (order == null) {
        return response.notFound()
      }

      order.transactionStatusId = 3 //paid

      const captureOrder = await Database.transaction(async (trx) => {
        try {
          await order.useTransaction(trx).save()

          const capture = {
            transaction: {
              kind: 'capture',
            },
          }

          const captureOrder = await shopifyClient.post(
            `/orders/${order.platformOrderId}/transactions.json`,
            capture
          )

          return captureOrder
        } catch (e) {
          throw new Exception(e)
        }
      })

      return response.ok(captureOrder)
    } catch (e) {
      return response.badRequest(e)
    }
  }

  public async importFromShopify({ response }: HttpContextContract) {
    const orders: any[] = []
    var res: AxiosResponse | null = null
    var rel: string = ''
    var query = 'status=open&limit=250'
    do {
      try {
        res = await shopifyClient.get(`/orders.json?${query}`)
        rel = res?.headers['link']
          ? res?.headers['link'].split(',').find((item) => item.split('; ')[1].includes('next')) ??
            ''
          : ''

        if (res?.headers['link'] && rel.includes('next')) {
          const link = res.headers['link']
            .split(',')
            .find((item) => item.split('; ')[1].includes('next'))
          query = link ? link.split('?')[1].split('>')[0] : ''
        }

        const shopifyOrders: any[] = res!.data['orders']
        if (shopifyOrders.length > 0) {
          const filteredOrder = shopifyOrders.filter((item) => {
            // const isFulfilled = item?.fulfillments?.length > 0
            const paid = item?.financial_status === 'paid'
            const authorized = item?.financial_status === 'authorized'
            // return !isFulfilled && (paid || authorized)
            return paid || authorized
          })
          const createdOrder = await importOrdersFromShopify(filteredOrder)
          orders.push(...createdOrder)
        }
      } catch (e) {
        //TODO optimize this
        console.log(e)
        res = null
        return response.internalServerError({
          message: 'error while syncing orders from shopify',
        })
      }
    } while (res?.headers['link'] && rel.includes('next'))
    await checkClosedOrders()
    console.log('import complete')
    return response.created({ data: orders, success: true })
  }

  public async importSingleFromShopify({ response, params: { id } }: HttpContextContract) {
    const {
      data: { order },
    } = await shopifyClient.get(`/orders/${id}.json`)
    const createdOrder = await importOrdersFromShopify([order])

    console.log('import complete')
    return response.created({ data: createdOrder, success: true })
  }

  public async getBlacklist({ response, params: { id } }: HttpContextContract) {
    const order = await Order.query()
      .where('id', id)
      .preload('customer', (query) => {
        query.preload('orders')
        query.preload('blacklist')
      })
      .first()

    if (!order) {
      return response.notFound()
    }

    //if there is a direct associated blacklist found, directly return that
    if (order.customer.blacklist) {
      return response.ok([order.customer.blacklist])
    }

    //otherwise a deeper search is needed
    const potentialBlacklists = await Blacklist.query()
      .join('customers', 'blacklists.customer_id', '=', 'customers.id')
      .join('orders', 'orders.customer_id', '=', 'customers.id')
      .orWhereIn(
        'orders.phone',
        order.customer.orders.map((item) => item.phone)
      )
      .orWhereIn(
        'orders.last_name',
        order.customer.orders.map((item) => item.lastName)
      )
      .orWhereIn(
        'orders.first_name',
        order.customer.orders.map((item) => item.firstName)
      )
      .orWhereIn(
        'orders.last_name',
        order.customer.orders.map((item) => item.firstName)
      )
      .orWhereIn(
        'orders.first_name',
        order.customer.orders.map((item) => item.lastName)
      )
      .orWhereIn(
        'orders.last_name',
        order.customer.orders.map((item) => item.firstName + item.lastName)
      )
      .orWhereIn(
        'orders.first_name',
        order.customer.orders.map((item) => item.firstName + item.lastName)
      )
      .orWhereIn(
        'orders.last_name',
        order.customer.orders.map((item) => item.lastName + item.firstName)
      )
      .orWhereIn(
        'orders.first_name',
        order.customer.orders.map((item) => item.lastName + item.firstName)
      )
      .orWhereIn(
        'orders.address',
        order.customer.orders.map((item) => item.address)
      )
      .orWhereIn(
        'orders.phone',
        order.customer.orders.map((item) => item.phone)
      )
      .select('blacklists.*')
      .groupBy('blacklists.id')

    return response.ok(potentialBlacklists)
  }
}

export async function createOrderPickingList(
  waitingShippedOnly: boolean = false,
  ignoreGenerated: boolean = false,
  ids: number[] = []
) {
  var orderQuery = Order.query()
  const kuroshiro = new Kuroshiro()
  await kuroshiro.init(new KuromojiAnalyzer())

  if (waitingShippedOnly) {
    orderQuery = orderQuery
      .andWhere('orders.order_status_id', 4)
      .andWhereIn('transaction_status_id', [1, 3])
  }

  if (ignoreGenerated) {
    orderQuery = orderQuery.andWhere('orders.generated', false)
  }

  if (ids && ids.length > 0) {
    orderQuery = orderQuery.andWhereIn('orders.id', ids)
  }

  // const jconv = require('jaconv')
  const appSetting = await AppSetting.query()
    .preload('fulfillmentSetting')
    .preload('yamatoSetting')
    .first()
  const yamatoSetting = appSetting?.yamatoSetting
  const fulfillmentSetting = appSetting?.fulfillmentSetting
  if (!yamatoSetting) {
    throw new Exception('Yamato settings could not be found')
  }
  if (!fulfillmentSetting) {
    throw new Exception('Fulfillment settings could not be found')
  }
  const filepaths: string[] = []
  const ungroupedData: any[] = []
  const date = new Date()
  const dateArr = date.toISOString().split('T')[0].split('-')
  const dateStr = `${dateArr[0]}-${dateArr[1]}-${dateArr[2]}`

  const pickingList = await orderQuery
    .join('line_items', 'orders.id', '=', 'line_items.order_id')
    .leftJoin('fulfillment_line_items', 'line_items.id', '=', 'fulfillment_line_items.line_item_id')
    .leftJoin('events', 'events.id', '=', 'fulfillment_line_items.event_id')
    .select('line_items.product_title')
    .select('line_items.variant_title')
    .select('line_items.sku as variant_sku')
    .sum('line_items.quantity as total_number')
    .sum('events.quantity as total_fulfilled')
    .groupBy(['variant_sku', 'line_items.product_title', 'line_items.variant_title'])
    .orderBy('line_items.product_title')

  if (pickingList.length < 1) {
    return []
  }

  for (const pickingItem of pickingList) {
    const fulfillableQty =
      (pickingItem.$extras?.total_number ?? 0) + (pickingItem.$extras?.total_fulfilled ?? 0)

    if (fulfillableQty <= 0) continue

    const fullname = `${pickingItem.$extras?.product_title ?? 'undefined'} - ${
      pickingItem.$extras?.variant_title ?? 'undefined'
    }`
    var data = {
      品名: fullname,
      SKU: pickingItem.$extras.variant_sku,
      // 数: pickingItem.$extras?.total_number,
      数: fulfillableQty,
    }

    ungroupedData.push(data)
  }

  await new ObjectToCsv(ungroupedData).toDisk(
    `${process.env.CSV_DIR}/${dateStr}-picking-list.csv`,
    {
      showHeader: true,
    }
  ) //save to disk

  filepaths.push(`${dateStr}-picking-list.csv`)
  // }

  return filepaths
}
