import { schema } from '@ioc:Adonis/Core/Validator'
import { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import Admin from 'App/Models/Admin'
import User from 'App/Models/User'

export default class UsersController {
  public async find({ response, request }: HttpContextContract) {
    const page = request.input('page', 1)
    const limit = request.input('limit', 20)
    const sort = request.input('sort', 'id:desc').split(':')
    const users = await User.filter(request.all())
      .preload('admin')
      .orderBy(sort[0], sort[1])
      .paginate(page, limit)

    return response.ok(users)
  }

  public async findOne({ response, params }: HttpContextContract) {
    const user = await User.query().where('id', params.id).preload('admin').first()
    if (user == null) {
      return response.notFound()
    }

    return response.ok(user)
  }

  public async updatePermission({ request, response, params: { id } }: HttpContextContract) {
    const postSchema = schema.create({
      products_access: schema.boolean.optional(),
      reviews_access: schema.boolean.optional(),
      user_permissions_access: schema.boolean.optional(),
      users_access: schema.boolean.optional(),
      stocks_access: schema.boolean.optional(),
      events_access: schema.boolean.optional(),
      warehouses_access: schema.boolean.optional(),
      returns_access: schema.boolean.optional(),
      sources_access: schema.boolean.optional(),
      orders_access: schema.boolean.optional(),
      orders_edit_access: schema.boolean.optional(),
      customers_access: schema.boolean.optional(),
      blacklists_access: schema.boolean.optional(),
    })
    const user = await User.query().where('id', id).preload('admin').first()

    if (user != null) {
      const payload = await request.validate({ schema: postSchema })
      const updatedAdmin = await Admin.firstOrCreate({
        userId: user.id,
      }) //await Admin.query().where('id', user.admin.id).update(payload)
      updatedAdmin.merge(payload as any)
      await updatedAdmin.save()
      return response.ok(updatedAdmin)
    }

    return response.badRequest()
  }
}
