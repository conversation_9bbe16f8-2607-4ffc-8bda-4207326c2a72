import { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import puppeteer from 'puppeteer'

export default class PuppeteersController {
  public async scrape({ request, response }: HttpContextContract) {
    console.log('scraping')
    console.log(request.all())

    try {
      const { url, keyword } = request.body()

      if (!url || !keyword) {
        return response.badRequest({ error: 'URL and keyword are required' })
      }

      const browser = await puppeteer.launch({
        headless: true,
        args: ['--no-sandbox', '--disable-setuid-sandbox'],
      })

      try {
        const page = await browser.newPage()
        page.setUserAgent(
          'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3029.110 Safari/537.3'
        )

        // Navigate to the URL
        await page.goto(url, {
          waitUntil: 'networkidle0', // Wait until network is idle
          timeout: 10000, // 30 seconds timeout
        })

        // Wait for any dynamic content to load
        await new Promise((resolve) => setTimeout(resolve, 2000)) // Additional 2 seconds wait for any lazy loading

        // Get the page content
        const content = await page.content()

        // Check if keyword exists in the content
        const keywordFound = content.toLowerCase().includes(keyword.toLowerCase())

        await browser.close()

        return response.ok({
          success: true,
          data: {
            keyword_found: keywordFound,
            url,
            keyword,
          },
        })
      } catch (error) {
        await browser.close()
        throw error
      }
    } catch (error) {
      return response.internalServerError({
        success: false,
        error: error.message,
      })
    }
  }
}
