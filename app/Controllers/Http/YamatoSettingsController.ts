import { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import YamatoSetting from 'App/Models/YamatoSetting'
import { schema, rules } from '@ioc:Adonis/Core/Validator'
import AppSetting from 'App/Models/AppSetting'

export default class YamatoSettingsController {
  public async find({ request, response }: HttpContextContract) {
    const page = request.input('page', 1)
    const limit = request.input('limit', 20)
    const sort = request.input('sort', 'id:desc').split(':')

    const yamatoSetting = await YamatoSetting.query()
      .preload('appSetting')
      .orderBy(sort[0], sort[1])
      .paginate(page, limit)
    return response.ok(yamatoSetting)
  }

  public async findOne({ response, params: { id } }: HttpContextContract) {
    const result = await YamatoSetting.query().where('id', id).preload('appSetting').first()
    if (result == null) {
      return response.notFound()
    }

    return response.ok(result)
  }

  public async create({ request, response }: HttpContextContract) {
    const postSchema = schema.create({
      setting_name: schema.string({ trim: true }),
      customer_no: schema.string({ trim: true }, [rules.minLength(12), rules.maxLength(12)]),
      unchin_no: schema.string.optional({ trim: true }, [rules.minLength(2), rules.maxLength(2)]),
      kurijou_type: schema.string.optional({ trim: true }, [
        rules.minLength(1),
        rules.maxLength(1),
      ]),
      handling_1: schema.string.optional({ trim: true }),
      handling_2: schema.string.optional({ trim: true }),
      timezone: schema.string.optional({}, [rules.minLength(4), rules.maxLength(4)]),
    })
    const isDefault = request.input('is_default')

    const payload = await request.validate({
      schema: postSchema,
    })

    try {
      const createYamatoSetting = await YamatoSetting.create({
        customerNo: payload.customer_no,
        unchinNo: payload.unchin_no,
        settingName: payload.setting_name,
        kurijouType: payload.kurijou_type,
      })

      if (isDefault) {
        const appSetting = await AppSetting.first()
        if (appSetting == null) {
          return response.notFound()
        }
        try {
          appSetting.yamatoSettingId = createYamatoSetting.id
          await appSetting.save()
        } catch (e) {
          return response.badRequest(e)
        }
      }

      response.created({ data: createYamatoSetting, success: true })
    } catch (e) {
      return response.internalServerError(e)
    }
  }

  public async update({ request, response, params: { id } }: HttpContextContract) {
    const yamatoSetting = await YamatoSetting.find(id)
    if (yamatoSetting == null) {
      return response.notFound()
    }

    const updateSchema = schema.create({
      setting_name: schema.string({ trim: true }),
      customer_no: schema.string({ trim: true }, [rules.minLength(12), rules.maxLength(12)]),
      unchin_no: schema.string.optional({ trim: true }, [rules.minLength(2), rules.maxLength(2)]),
      kurijou_type: schema.string.optional({ trim: true }, [
        rules.minLength(1),
        rules.maxLength(1),
      ]),
      handling_1: schema.string.optional({ trim: true }),
      handling_2: schema.string.optional({ trim: true }),
      timezone: schema.string.optional({}, [rules.minLength(4), rules.maxLength(4)]),
    })
    const isDefault = request.input('is_default')

    try {
      const payload = await request.validate({ schema: updateSchema })
      const camelPayload = {
        ...payload,
        settingName: payload.setting_name,
      }
      yamatoSetting.merge(camelPayload)
      await yamatoSetting.save()

      if (isDefault) {
        const appSetting = await AppSetting.first()
        if (appSetting == null) {
          return response.notFound()
        }
        try {
          appSetting.yamatoSettingId = yamatoSetting.id
          await appSetting.save()
        } catch (e) {
          return response.badRequest(e)
        }
      }

      return response.ok({ data: yamatoSetting, success: true })
    } catch (e) {
      return response.badRequest(e)
    }
  }

  public async delete({ response, params: { id } }: HttpContextContract) {
    const yamatoSetting = await YamatoSetting.find(id)

    if (yamatoSetting == null) {
      return response.notFound()
    }

    if (yamatoSetting.isDefault) {
      return response.badRequest({ message: 'Unable to delete default yamato setting ' })
    }

    try {
      await yamatoSetting.delete()
      return response.ok({ data: yamatoSetting, success: true })
    } catch (e) {
      return response.badRequest(e)
    }
  }
}
