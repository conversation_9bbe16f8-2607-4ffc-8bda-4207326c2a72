import { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import { schema } from '@ioc:Adonis/Core/Validator'
import { v4 as uuidv4 } from 'uuid'
import _ from 'lodash'
import { DateTime } from 'luxon'
import shopifyClient from 'App/Network/shopifyClient'

const quantityToValue = [
  { min: 5, max: 19, price: -100 },
  { min: 20, max: 49, price: -200 },
  { min: 50, max: 99, price: -300 },
  { min: 100, price: -400 },
]

export default class DiscountsController {
  public async createPriceRuleDiscount({ request, response }: HttpContextContract) {
    const validateDataSchema = schema.create({
      type: schema.enum(['company', 'bulk']),
      quantity: schema.number(),
      discountValue: schema.number(),
      discountType: schema.enum(['percentage', 'fixed_amount']),
    })

    const validateData = await request.validate({
      schema: validateDataSchema,
    })

    console.log(validateData)

    const title = validateData.type === 'company' ? `COMPANYDISCOUNT` : `BULKDISCOUNT`
    const code = validateData.type === 'company' ? `co-${uuidv4()}` : `bk-${uuidv4()}`
    // TODO: how to pass quantityToValue from front end
    const value =
      validateData.discountType == 'percentage'
        ? -validateData.discountValue
        : _.find(quantityToValue, (item) => {
            if (item.max) {
              return item.min <= validateData.quantity && item.max >= validateData.quantity
            }
            return false
          })?.price ?? 0 // TODO: throw if not found

    console.log({
      title: title,
      target_type: 'line_item',
      target_selection: 'all',
      allocation_method: 'across',
      value_type: validateData.discountType,
      value: value,
      usage_limit: 1,
      customer_selection: 'all',
      starts_at: DateTime.now().toISO(),
      ends_at: DateTime.now().plus({ hours: 1 }).toISO(),
    })

    const priceRuleResponse: any = await shopifyClient.post(`/price_rules.json`, {
      price_rule: {
        title: title,
        target_type: 'line_item',
        target_selection: 'all',
        allocation_method: 'across',
        value_type: validateData.discountType,
        value: value,
        usage_limit: 1,
        customer_selection: 'all',
        starts_at: DateTime.now().toISO(),
        ends_at: DateTime.now().plus({ hours: 1 }).toISO(),
      },
    })

    console.log(priceRuleResponse)

    const discountCodeResponse = await shopifyClient.post(
      `/price_rules/${priceRuleResponse.data.price_rule.id}/discount_codes.json`,
      {
        discount_code: {
          code: code,
        },
      }
    )

    console.log(discountCodeResponse)

    return response.status(200).send({
      message: 'Price rule created',
      success: true,
      discount: discountCodeResponse.data.discount_code,
    })
  }
}
