import { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import { schema } from '@ioc:Adonis/Core/Validator'
import axios from 'axios'

export default class KlaviyosController {
  public async subscribe({ request, response }: HttpContextContract) {
    const klaviyoApiKey = process.env.KLAVIYO_API_KEY

    if (!klaviyoApiKey) {
      return response.internalServerError('klaviyo api key was not found')
    }
    const postSchema = schema.create({
      list_id: schema.string(),
      profiles: schema.array().members(
        schema.object().members({
          $consent: schema.string(),
          email: schema.string(),
          name: schema.string.optional(),
        })
      ),
    })

    try {
      var data: any = null
      var error: any = null
      const payload = await request.validate({ schema: postSchema })
      await axios
        .post(`https://a.klaviyo.com/api/v2/list/${payload.list_id}/subscribe`, {
          api_key: klaviyo<PERSON><PERSON><PERSON><PERSON>,
          profiles: payload.profiles,
        })
        .then((res) => (data = res.data))
        .catch((e) => {
          error = e?.response?.data ?? e
        })

      if (data) {
        return response.ok(data)
      }

      return response.badRequest(error)
    } catch (e) {
      return response.badRequest(e)
    }
  }
}
