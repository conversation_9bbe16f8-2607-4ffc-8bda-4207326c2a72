import { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import { schema } from '@ioc:Adonis/Core/Validator'
import { v4 as uuid } from 'uuid'
import fs from 'fs'
import * as AWS from 'aws-sdk'

// === CONFIG ===
// 🚨 Don't forget to configure CORS in Digital Ocean Spaces
const BUCKET = 'casefinite'
const REGION = 'sgp1'
const SECRET = undefined // (Optional) set a secret. Needs to be added to the Sanity plugin as well!

const ENDPOINT = `${REGION}.digitaloceanspaces.com`
const spacesEndpoint = new AWS.Endpoint(ENDPOINT)
const credentials = new AWS.Credentials({
  accessKeyId: process.env.SPACES_KEY!,
  secretAccessKey: process.env.SPACES_SECRET!,
})
const ergofiniteCredentials = new AWS.Credentials({
  accessKeyId: process.env.ERGO_SPACES_KEY!,
  secretAccessKey: process.env.ERGO_SPACES_SECRET!,
})
const coffeeCredentials = new AWS.Credentials({
  accessKeyId: process.env.COFFEE_SPACES_KEY!,
  secretAccessKey: process.env.COFFEE_SPACES_SECRET!,
})

const s3 = new AWS.S3({
  endpoint: spacesEndpoint,
  credentials,
})

const ergofiniteS3 = new AWS.S3({
  endpoint: spacesEndpoint,
  credentials: ergofiniteCredentials,
})

const coffeeS3 = new AWS.S3({
  endpoint: spacesEndpoint,
  credentials: coffeeCredentials,
})

function getRandomKey() {
  return Math.random().toFixed(10).replace('0.', '')
}

export const uploadToSpacesBucket = async (
  file: any,
  bucket: string,
  userProfile?: string
): Promise<{ key: string; url: string }> => {
  try {
    const { type, subtype, extname } = file
    let mimeType = type + '/' + subtype
    const name = userProfile ? userProfile + '/' + uuid() + '.' + extname : uuid() + '.' + extname
    const readStream = fs.createReadStream(file.tmpPath)

    const uploadRes = await s3
      .upload({
        Key: name,
        Bucket: bucket,
        ContentType: mimeType,
        Body: readStream,
        ACL: 'public-read',
      })
      .promise()

    return {
      key: name,
      url: uploadRes.Location,
    }
  } catch (err) {
    console.log(err)
    return err
  }
}

export default class FilesController {
  public async upload({ request, response }: HttpContextContract) {
    const validationSchema = schema.create({
      file: schema.file({
        size: '10mb',
        extnames: ['pdf', 'png', 'jpg', 'jpeg', 'txt'],
      }),
    })

    const validationData = await request.validate({
      schema: validationSchema,
    })

    try {
      if (validationData.file) {
        const uploadBucket = await uploadToSpacesBucket(validationData.file, BUCKET)
        return response.send({
          success: true,
          data: uploadBucket.url,
        })
      }
    } catch (error) {
      console.log(error)
      return response.status(400).send({
        success: false,
        data: null,
      })
    }
  }

  public async getSignedUrl({ request, response }: HttpContextContract) {
    const validationSchema = schema.create({
      secret: schema.string.optional(),
      fileName: schema.string(),
      contentType: schema.string(),
    })

    const validationData = await request.validate({
      schema: validationSchema,
    })

    try {
      if (typeof SECRET !== 'undefined' && validationData.secret !== SECRET) {
        return response.status(401).send({
          message: 'Unauthorized',
        })
      }

      const s3Response = await s3.createPresignedPost({
        Fields: {
          key:
            validationData.fileName ||
            `${getRandomKey()}-${getRandomKey()}-${validationData.contentType || 'unknown-type'}`,
          acl: 'public-read',
        },
        Conditions: validationData.contentType
          ? [['eq', '$Content-Type', validationData.contentType]]
          : [],
        Expires: 30,
        Bucket: BUCKET,
      })

      return response.send(s3Response)
    } catch (error) {
      console.log(error)
      return response.status(400).send({
        success: false,
        data: null,
      })
    }
  }

  public async deleteObject({ request, response }: HttpContextContract) {
    const validationSchema = schema.create({
      secret: schema.string.optional(),
      fileKey: schema.string(),
    })

    const validationData = await request.validate({
      schema: validationSchema,
    })

    try {
      if (typeof SECRET !== 'undefined' && validationData.secret !== SECRET) {
        return response.status(401).send({
          message: 'Unauthorized',
        })
      }

      if (!validationData.fileKey || typeof validationData.fileKey !== 'string') {
        return response.status(400).send({
          message: 'Missing file key',
        })
      }

      await s3
        .deleteObject({
          Bucket: BUCKET,
          Key: validationData.fileKey,
        })
        .promise()

      return response.ok({ message: 'Success!' })
    } catch (error) {
      console.log(error)
      return response.status(400).send({
        success: false,
        data: null,
      })
    }
  }

  public async getErgofiniteSignedUrl({ request, response }: HttpContextContract) {
    const validationSchema = schema.create({
      secret: schema.string.optional(),
      fileName: schema.string(),
      contentType: schema.string(),
    })

    const validationData = await request.validate({
      schema: validationSchema,
    })

    try {
      if (typeof SECRET !== 'undefined' && validationData.secret !== SECRET) {
        return response.status(401).send({
          message: 'Unauthorized',
        })
      }

      const s3Response = await ergofiniteS3.createPresignedPost({
        Fields: {
          key:
            validationData.fileName ||
            `${getRandomKey()}-${getRandomKey()}-${validationData.contentType || 'unknown-type'}`,
          acl: 'public-read',
        },
        Conditions: validationData.contentType
          ? [['eq', '$Content-Type', validationData.contentType]]
          : [],
        Expires: 30,
        Bucket: 'ergofinite',
      })

      return response.send(s3Response)
    } catch (error) {
      console.log(error)
      return response.status(400).send({
        success: false,
        data: null,
      })
    }
  }

  public async deleteErgofiniteObject({ request, response }: HttpContextContract) {
    const validationSchema = schema.create({
      secret: schema.string.optional(),
      fileKey: schema.string(),
    })

    const validationData = await request.validate({
      schema: validationSchema,
    })

    try {
      if (typeof SECRET !== 'undefined' && validationData.secret !== SECRET) {
        return response.status(401).send({
          message: 'Unauthorized',
        })
      }

      if (!validationData.fileKey || typeof validationData.fileKey !== 'string') {
        return response.status(400).send({
          message: 'Missing file key',
        })
      }

      await ergofiniteS3
        .deleteObject({
          Bucket: 'ergofinite',
          Key: validationData.fileKey,
        })
        .promise()

      return response.ok({ message: 'Success!' })
    } catch (error) {
      console.log(error)
      return response.status(400).send({
        success: false,
        data: null,
      })
    }
  }
  public async getCoffeeSignedUrl({ request, response }: HttpContextContract) {
    const validationSchema = schema.create({
      secret: schema.string.optional(),
      fileName: schema.string(),
      contentType: schema.string(),
    })

    const validationData = await request.validate({
      schema: validationSchema,
    })

    try {
      if (typeof SECRET !== 'undefined' && validationData.secret !== SECRET) {
        return response.status(401).send({
          message: 'Unauthorized',
        })
      }

      const s3Response = await coffeeS3.createPresignedPost({
        Fields: {
          key:
            validationData.fileName ||
            `${getRandomKey()}-${getRandomKey()}-${validationData.contentType || 'unknown-type'}`,
          acl: 'public-read',
        },
        Conditions: validationData.contentType
          ? [['eq', '$Content-Type', validationData.contentType]]
          : [],
        Expires: 30,
        Bucket: '103coffee',
      })

      return response.send(s3Response)
    } catch (error) {
      console.log(error)
      return response.status(400).send({
        success: false,
        data: null,
      })
    }
  }

  public async deleteCoffeeObject({ request, response }: HttpContextContract) {
    const validationSchema = schema.create({
      secret: schema.string.optional(),
      fileKey: schema.string(),
    })

    const validationData = await request.validate({
      schema: validationSchema,
    })

    try {
      if (typeof SECRET !== 'undefined' && validationData.secret !== SECRET) {
        return response.status(401).send({
          message: 'Unauthorized',
        })
      }

      if (!validationData.fileKey || typeof validationData.fileKey !== 'string') {
        return response.status(400).send({
          message: 'Missing file key',
        })
      }

      await coffeeS3
        .deleteObject({
          Bucket: '103coffee',
          Key: validationData.fileKey,
        })
        .promise()

      return response.ok({ message: 'Success!' })
    } catch (error) {
      console.log(error)
      return response.status(400).send({
        success: false,
        data: null,
      })
    }
  }
}
