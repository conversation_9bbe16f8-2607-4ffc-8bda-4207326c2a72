import Logger from '@ioc:Adonis/Core/Logger'
import Fs from 'fs'
import ObjectToCsv from 'node-create-csv'
import KuromojiAnalyzer from 'kuroshiro-analyzer-kuromoji'
import <PERSON><PERSON><PERSON> from 'kuroshiro'
import { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import { schema } from '@ioc:Adonis/Core/Validator'
import Fulfillment from 'App/Models/Fulfillment'
import { DateTime } from 'luxon'
import Order from 'App/Models/Order'

export default class ReportsController {
  public async fulfillmentReport({ request, response }: HttpContextContract) {
    const sort = request.input('sort', 'total:desc').split(':')
    try {
      const reportSchema = schema.create({
        starts_at: schema.date.optional(),
        ends_at: schema.date.optional(),
      })
      const payload = await request.validate({ schema: reportSchema })

      const kuroshiro = new <PERSON><PERSON>hiro()
      await kuroshiro.init(new KuromojiAnalyzer())

      const ungroupedData: any[] = []
      const date = new Date()
      const dateArr = date.toISOString().split('T')[0].split('-')
      const dateStr = `${dateArr[0]}-${dateArr[1]}-${dateArr[2]}`

      const defaultStart = DateTime.local(
        DateTime.now().year,
        DateTime.now().month,
        DateTime.now().day
      ).minus({ days: 1 })
      const defaultEnd = defaultStart.plus({ days: 1 })

      const reportData = await Fulfillment.query()
        .join('orders', 'orders.id', 'fulfillments.order_id')
        .join('fulfillment_line_items', 'fulfillments.id', 'fulfillment_line_items.fulfillment_id')
        .join('events', 'events.id', 'fulfillment_line_items.event_id')
        .join('line_items', 'line_items.id', '=', 'fulfillment_line_items.line_item_id')
        .where('orders.fulfilled_at', '>=', payload.starts_at?.toSQL() ?? defaultStart.toSQL())
        .where('orders.fulfilled_at', '<', payload.ends_at?.toSQL() ?? defaultEnd.toSQL())
        .select('line_items.product_title')
        .select('line_items.variant_title')
        .select('line_items.sku as variant_sku')
        .sum('events.quantity as total')
        .groupBy(['variant_sku', 'line_items.product_title', 'line_items.variant_title'])
        .orderBy(sort[0], sort[1])

      for (const pickingItem of reportData) {
        const fullname = `${pickingItem.$extras?.product_title ?? 'undefined'} - ${
          pickingItem.$extras?.variant_title ?? 'undefined'
        }`
        var data = {
          品名: fullname,
          SKU: pickingItem.$extras.variant_sku,
          // 数: pickingItem.$extras?.total_number,
          数: Math.abs(pickingItem.$extras.total),
        }

        ungroupedData.push(data)
      }

      const filePath = `${process.env.CSV_DIR}/${dateStr}-fulfilled-list.csv`
      await new ObjectToCsv(ungroupedData).toDisk(filePath, {
        showHeader: true,
      }) //save to disk

      // }

      response.stream(Fs.createReadStream(filePath))
    } catch (e) {
      Logger.error('fulfillment report generation error')
      Logger.error(e)
      return response.badRequest(e)
    }
  }

  public async pendingFulfillmentReport({ request, response }: HttpContextContract) {
    const sort = request.input('sort', 'total:desc').split(':')
    try {
      const reportSchema = schema.create({
        starts_at: schema.date.optional(),
        ends_at: schema.date.optional(),
      })
      const payload = await request.validate({ schema: reportSchema })

      const kuroshiro = new Kuroshiro()
      await kuroshiro.init(new KuromojiAnalyzer())

      const ungroupedData: any[] = []
      const date = new Date()
      const dateArr = date.toISOString().split('T')[0].split('-')
      const dateStr = `${dateArr[0]}-${dateArr[1]}-${dateArr[2]}`

      const defaultStart = DateTime.local(
        DateTime.now().year,
        DateTime.now().month,
        DateTime.now().day
      ).minus({ days: 1 })
      const defaultEnd = defaultStart.plus({ days: 1 })

      const reportData = await Order.query()
        .join('line_items', 'line_items.order_id', '=', 'orders.id')
        .where('orders.created_at', '>=', payload.starts_at?.toSQL() ?? defaultStart.toSQL())
        .where('orders.created_at', '<', payload.ends_at?.toSQL() ?? defaultEnd.toSQL())
        .select('line_items.product_title')
        .select('line_items.variant_title')
        .select('line_items.sku as variant_sku')
        .sum('line_items.quantity as total')
        .groupBy(['variant_sku', 'line_items.product_title', 'line_items.variant_title'])
        .orderBy(sort[0], sort[1])

      for (const pickingItem of reportData) {
        const fullname = `${pickingItem.$extras?.product_title ?? 'undefined'} - ${
          pickingItem.$extras?.variant_title ?? 'undefined'
        }`
        var data = {
          品名: fullname,
          SKU: pickingItem.$extras.variant_sku,
          // 数: pickingItem.$extras?.total_number,
          数: Math.abs(pickingItem.$extras.total),
        }

        ungroupedData.push(data)
      }

      const filePath = `${process.env.CSV_DIR}/${dateStr}-pending-fulfillment-list.csv`
      await new ObjectToCsv(ungroupedData).toDisk(filePath, {
        showHeader: true,
      }) //save to disk

      // }

      response.stream(Fs.createReadStream(filePath))
    } catch (e) {
      Logger.error('fulfillment report generation error')
      Logger.error(e)
      return response.badRequest(e)
    }
  }
}
