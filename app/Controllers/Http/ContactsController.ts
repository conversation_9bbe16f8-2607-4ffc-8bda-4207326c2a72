import { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import { schema } from '@ioc:Adonis/Core/Validator'
import Mail from '@ioc:Adonis/Addons/Mail'
import Contact from 'App/Models/Contact'

export default class ContactsController {
  public async find({ request, response }: HttpContextContract) {
    const page = request.input('page', 1)
    const limit = request.input('limit', 20)
    const sort = request.input('sort', 'id:desc').split(':')

    const contacts = await Contact.filter(request.all())
      .orderBy(sort[0], sort[1])
      .paginate(page, limit)

    return response.ok(contacts)
  }

  public async findOne({ response, params }: HttpContextContract) {
    const contact = await await Contact.query().where('id', params.id).first()

    if (contact != null) {
      return response.ok(contact)
    }

    return response.notFound()
  }

  public async create({ request, response }: HttpContextContract) {
    const validationSchema = schema.create({
      name: schema.string(),
      email: schema.string(),
      message: schema.string.optional(),
      phone: schema.string(),
    })

    const validationData = await request.validate({
      schema: validationSchema,
    })

    try {
      const createContact = await Contact.create({
        ...validationData,
      })

      await Mail.send((message) => {
        message
          .from('<EMAIL>')
          .to('<EMAIL>')
          .subject('You have a new contact enquiry!')
          .htmlView('emails/contact', {
            name: createContact.name ?? '',
            email: createContact.email ?? '',
            phone: createContact.phone ?? '',
            message: createContact.message ?? '',
          })
      })

      return response.created(createContact)
    } catch (e) {
      return response.badRequest(e)
    }
  }
}
