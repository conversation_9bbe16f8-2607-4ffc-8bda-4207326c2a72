import { schema, rules } from '@ioc:Adonis/Core/Validator'
import { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import shopifyClient from 'App/Network/shopifyClient'

export default class ShopifyCustomersController {
  public async create({ request, response }: HttpContextContract) {
    const customerSchema = schema.create({
      email: schema.string({ trim: true }, [rules.email()]),
    })

    try {
      const payload = await request.validate({ schema: customerSchema })
      const createdCostomer = await shopifyClient.post(`/customers.json`, {
        customer: payload,
      })

      return response.json(createdCostomer.data)
    } catch (e) {
      return response.badRequest(e)
    }
  }
}
