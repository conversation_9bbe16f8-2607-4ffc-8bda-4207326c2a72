import { adjustShopifyInventory } from 'App/Modules/ShopifyProductServices'
import { schema, rules } from '@ioc:Adonis/Core/Validator'
import { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import Stock from 'App/Models/Stock'
import { getStockWithQuantityById } from 'App/Modules/GetStockWithQuantity'
import Event from 'App/Models/Event'
import StockQuality from 'App/Models/StockQuality'
import Database from '@ioc:Adonis/Lucid/Database'
import ProductVariant from 'App/Models/ProductVariant'

export default class StocksController {
  public async find({ request, response }: HttpContextContract) {
    const page = request.input('page', 1)
    const limit = request.input('limit', 20)
    const sort = request.input('sort', 'id:desc').split(':')
    try {
      const stocks = await getStockWithQuantityById([], page, limit, sort)
      return response.ok(stocks)
    } catch (e) {
      console.log(e)
      return response.internalServerError(e)
    }
  }

  public async findOne({ response, params: { id } }: HttpContextContract) {
    try {
      const stock = await getStockWithQuantityById([id])
      if (stock[0] == null) {
        return response.notFound()
      }

      return response.ok(stock[0])
    } catch (e) {
      return response.internalServerError(e)
    }
  }

  public async create({ request, response }: HttpContextContract) {
    const postSchema = schema.create({
      warehouse_id: schema.number([
        rules.exists({
          table: 'warehouses',
          column: 'id',
        }),
      ]),
      variant_id: schema.number([
        rules.exists({
          table: 'product_variants',
          column: 'id',
        }),
      ]),
      quality_id: schema.number.optional([
        rules.exists({
          table: 'stock_qualities',
          column: 'id',
        }),
      ]),
      initial_stock: schema.number(),
    })

    try {
      const payload = await request.validate({ schema: postSchema })
      const exists = await Stock.query()
        .where('warehouse_id', payload.warehouse_id)
        .andWhere('variant_id', payload.variant_id)
        .andWhere('quality_id', payload.quality_id ?? 1)

      if (exists) {
        return response.badRequest({ message: 'this stock is already exist' })
      }
      const newStock = Database.transaction(async (trx) => {
        const variant = await ProductVariant.query()
          .where('id', payload.variant_id)
          .preload('product')
          .first()

        const newStock = await Stock.create(
          {
            warehouseId: payload.warehouse_id,
            variantId: payload.variant_id,
            qualityId: payload.quality_id,
          },
          { client: trx }
        )

        await Event.create(
          {
            stockId: newStock.id,
            quantity: payload.initial_stock,
            eventTypeId: 1,
            statusId: 1,
            sourceId: 1,
          },
          { client: trx }
        )

        if (variant && variant.product.shopifySync) {
          await adjustShopifyInventory(variant.shopifyId, payload.initial_stock)
        }

        return newStock
      })

      return response.created({ data: newStock, success: true })
    } catch (e) {
      return response.badRequest(e)
    }
  }

  public async update({ request, response, params: { id } }: HttpContextContract) {
    const postSchema = schema.create({
      warehouse_id: schema.number.optional(),
      variant_id: schema.number.optional(),
      quality_id: schema.number.optional([
        rules.exists({
          table: 'stock_qualities',
          column: 'id',
        }),
      ]),
    })
    try {
      const payload = await request.validate({ schema: postSchema })
      const stock = await Stock.query()
        .update(payload)
        .where('id', id)
        .andWhere('was_deleted', false)
        .first()
      if (stock == null) {
        return response.notFound()
      }

      return response.ok({ data: stock, success: true })
    } catch (e) {
      return response.badRequest(e)
    }
  }

  public async delete({ response, params: { id } }: HttpContextContract) {
    try {
      const stock = await Stock.query()
        .update({ was_deleted: true })
        .where('id', id)
        .andWhere('was_deleted', false)
        .first()
      if (stock == null) {
        return response.notFound()
      }

      return response.ok(stock)
    } catch (e) {
      return response.badRequest(e)
    }
  }

  public async adjust({ request, response, params: { id } }: HttpContextContract) {
    const stock = await Stock.query()
      .where('id', id)
      .preload('variant', (query) => {
        query.preload('product')
      })
      .first()

    if (!stock) {
      return response.notFound({ message: 'not found' })
    }

    const postSchema = schema.create({
      quantity: schema.number(),
    })
    try {
      const payload = await request.validate({ schema: postSchema })
      await Event.create({
        stockId: stock.id,
        quantity: payload.quantity,
        remark: 'stock adjustment',
        eventTypeId: 1,
        statusId: 1,
        sourceId: 1,
      })

      if (stock.variant && stock.variant.product.shopifySync) {
        await adjustShopifyInventory(stock.variant.shopifyId, payload.quantity)
      }

      return response.ok({ data: stock, success: true })
    } catch (e) {
      return response.badRequest(e)
    }
  }

  public async getQualities({ response }: HttpContextContract) {
    const result = await StockQuality.all()
    return response.ok(result)
  }
}
