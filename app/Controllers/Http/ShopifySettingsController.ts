import { schema } from '@ioc:Adonis/Core/Validator'
import ShopifySetting from 'App/Models/ShopifySetting'
import { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import shopifyClient from 'App/Network/shopifyClient'
import AppSetting from 'App/Models/AppSetting'

export default class ShopifySettingsController {
  public async find({ request, response }: HttpContextContract) {
    const page = request.input('page', 1)
    const limit = request.input('limit', 20)
    const sort = request.input('sort', 'id:desc').split(':')

    const shopifySetting = await ShopifySetting.query()
      .preload('appSetting')
      .orderBy(sort[0], sort[1])
      .paginate(page, limit)
    return response.ok(shopifySetting)
  }

  public async findOne({ response, params: { id } }: HttpContextContract) {
    const result = await ShopifySetting.query().where('id', id).preload('appSetting').first()
    if (result == null) {
      return response.notFound()
    }

    return response.ok(result)
  }

  public async create({ request, response }: HttpContextContract) {
    const postSchema = schema.create({
      setting_name: schema.string({ trim: true }),
      out_location_id: schema.string({ trim: true }),
    })
    const isDefault = request.input('is_default')

    try {
      const payload = await request.validate({
        schema: postSchema,
      })

      const createShopifySetting = await ShopifySetting.create({
        ...payload,
        outLocationId: payload.out_location_id,
      })
      if (isDefault) {
        const appSetting = await AppSetting.first()
        if (appSetting == null) {
          return response.notFound()
        }
        try {
          appSetting.shopifySettingId = createShopifySetting.id
          await appSetting.save()
        } catch (e) {
          return response.badRequest(e)
        }
      }

      response.created({ data: createShopifySetting, success: true })
    } catch (e) {
      return response.internalServerError(e)
    }
  }

  public async update({ request, response, params: { id } }: HttpContextContract) {
    const shopifySetting = await ShopifySetting.find(id)
    if (shopifySetting == null) {
      return response.notFound()
    }

    const updateSchema = schema.create({
      setting_name: schema.string({ trim: true }),
      out_location_id: schema.string({ trim: true }),
    })
    const isDefault = request.input('is_default')

    try {
      const payload = await request.validate({ schema: updateSchema })

      shopifySetting.merge({
        ...payload,
        outLocationId: payload.out_location_id,
      })
      await shopifySetting.save()

      if (isDefault) {
        const appSetting = await AppSetting.first()
        if (appSetting == null) {
          return response.notFound()
        }
        try {
          appSetting.shopifySettingId = shopifySetting.id
          await appSetting.save()
        } catch (e) {
          return response.badRequest(e)
        }
      }

      return response.ok({ data: shopifySetting, success: true })
    } catch (e) {
      console.log(e)
      return response.badRequest(e)
    }
  }

  public async delete({ response, params: { id } }: HttpContextContract) {
    const shopifySetting = await ShopifySetting.find(id)

    if (shopifySetting == null) {
      return response.notFound()
    }

    if (shopifySetting.isDefault) {
      return response.badRequest({ message: 'Unable to delete default fulfillment setting ' })
    }

    try {
      await shopifySetting.delete()
      return response.ok({ data: shopifySetting, success: true })
    } catch (e) {
      return response.badRequest(e)
    }
  }
  public async getLocations({ response }: HttpContextContract) {
    try {
      const {
        data: { locations: shopifyLocations },
      } = await shopifyClient.get('/locations.json')
      return response.ok(shopifyLocations)
    } catch (e) {
      console.log('get shopify locations', e)
      return response.internalServerError(e)
    }
  }
}
