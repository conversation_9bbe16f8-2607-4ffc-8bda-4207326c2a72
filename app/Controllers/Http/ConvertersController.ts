import shopifyClient from 'App/Network/shopifyClient'
import { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import <PERSON><PERSON><PERSON> from 'kuroshiro'
import KuromojiAnalyzer from 'kuroshiro-analyzer-kuromoji'
export default class ConvertersController {
  public async webHookOrder({ request }: HttpContextContract) {
    request.input('id')
    //webhook when order creation
    //next step should be in getOrder, but now can only webhook fake order, which cant be search
  }

  public async getOrder({ params, response }: HttpContextContract) {
    //get web hook from shopify
    //get the order detail form the same order id

    const findOrder = await shopifyClient.get(`/orders/${params.id}.json`)
    const order: any = findOrder.data['order']

    //convert the name to katakana as per required by yamato api reference
    const kuroshiro = new Kuroshiro()
    await kuroshiro.init(new KuromojiAnalyzer())
    const katakanaFirstName = await kuroshiro.convert(order.billing_address.first_name, {
      to: 'katakana',
    })
    const katakanaLastName = await kuroshiro.convert(order.billing_address.last_name, {
      to: 'katakana',
    })

    //get the data needed for Send to Yamato and for Order reference
    //Send to Yamato API
    //Save the return result of the Yamato (tracking code)

    //Json for testing
    const customerJson = [
      {
        iraiDataTime: order.created_at,
        dstTel: order.customer.phone,
        dstZipCd: order.billing_address.zip,
        dstAddress1: order.billing_address.province,
        dstAddress2: order.billing_address.city,
        dstAddress3: order.billing_address.address1,
        dstAddress4: order.billing_address.address2,
        dstCoNm: order.billing_address.company,
        dstLastName: order.billing_address.last_name,
        dstFirstNm: order.billing_address.first_name,
        baggDesc2: order.line_items.map((item) => item.name).join(', '),
        dstMailAddr: order.contact_email,
        shukaLastNm: order.billing_address.last_name,
        shukaFirstNm: order.billing_address.first_name,
        shukaLastNmKana: katakanaLastName,
        shukaFirstNmKana: katakanaFirstName,
        shukaZipCd: order.billing_address.zip,
        shukaAddress1: order.billing_address.address1,
        shukaAddress2: order.billing_address.address2,
        shukaAddress3: order.billing_address.address3,
        shukaKosu: order.line_items
          .map((item) => item.fulfillable_quantity)
          .reduce((sum, value) => sum + value, 0),
      },
    ]

    return response.ok(customerJson)
  }
}
