import { schema } from '@ioc:Adonis/Core/Validator'
import { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import Source from 'App/Models/Source'

export default class SourcesController {
  public async find({ request, response }: HttpContextContract) {
    const page = request.input('page', 1)
    const limit = request.input('limit', 20)
    const sort = request.input('sort', 'id:desc').split(':')
    const products = await Source.filter(request.all())
      .where('was_deleted', false)
      .orderBy(sort[0], sort[1])
      .paginate(page, limit)

    return response.ok(products)
  }

  public async findOne({ response, params: { id } }: HttpContextContract) {
    try {
      const source = await Source.query().where('id', id).andWhere('was_deleted', false).first()
      if (source == null) {
        return response.notFound()
      }

      return response.ok(source)
    } catch (e) {
      return response.internalServerError(e)
    }
  }

  public async create({ request, response }: HttpContextContract) {
    const postSchema = schema.create({
      source_name: schema.string({ trim: true }),
      sync: schema.boolean.optional(),
      secretKey: schema.string.optional({ trim: true }),
      apiKey: schema.string.optional({ trim: true }),
    })

    try {
      const payload = await request.validate({ schema: postSchema })
      const newSource = await Source.create({
        sourceName: payload.source_name,
      })
      return response.created(newSource)
    } catch (e) {
      return response.badRequest(e)
    }
  }

  public async update({ request, response, params: { id } }: HttpContextContract) {
    const source = await Source.find(id)

    if (source != null) {
      const postSchema = schema.create({
        source_name: schema.string.optional({ trim: true }),
        sync: schema.boolean.optional(),
        secretKey: schema.string.optional({ trim: true }),
        apiKey: schema.string.optional({ trim: true }),
      })

      try {
        const payload = await request.validate({ schema: postSchema })
        source.merge(payload)
        await source.save()
        return response.ok(source)
      } catch (e) {
        return response.badRequest(e)
      }
    }
    return response.notFound()
  }

  public async delete({ response, params: { id } }: HttpContextContract) {
    const source = await Source.find(id)
    if (source != null) {
      source.wasDeleted = true
      await source.save()
      return response.ok(source)
    }
    return response.notFound()
  }

  //TODO: just a placeholder, will be removed one secret key is embded in to sources/shop
  public async getShopifyToken({ response }: HttpContextContract) {
    return response.ok({
      admin_access: process.env['ADMIN_ACCESS'],
      api_key: process.env['API_KEY'],
      api_secret_key: process.env['API_SECRET_KEY'],
      shop: process.env['SHOP'],
      shopify_host: process.env['SHOPIFY_HOST'],
    })
  }
}
