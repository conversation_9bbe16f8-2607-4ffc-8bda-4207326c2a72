import { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import { schema } from '@ioc:Adonis/Core/Validator'
import Status from 'App/Models/Status'

export default class StatusesController {
  public async get({ response }: HttpContextContract) {
    const result = await Status.all()
    return response.ok(result)
  }

  public async create({ request, response }: HttpContextContract) {
    const statusSchema = schema.create({
      statusName: schema.string({ trim: true }),
    })

    try {
      const body = await request.validate({ schema: statusSchema })
      const status = await Status.create(body)
      return response.created(status)
    } catch (e) {
      return response.badRequest(e)
    }
  }

  public async update({ request, response, params }) {
    try {
      const body = request.body()
      const status = await Status.find(params.id)

      if (status == null) {
        return response.notFound()
      } else {
        status.statusName = body.status_name
        await status?.save()
        return response.ok(status)
      }
    } catch (e) {
      return response.internalServerError(e)
    }
  }
}
