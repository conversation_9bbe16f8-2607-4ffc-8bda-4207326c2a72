import { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import CustomerNotificationEmail from 'App/Modules/CustomerNotificationEmail'
import { rules, schema } from '@ioc:Adonis/Core/Validator'
import { emailTemplates } from 'App/Models/EmailTemplate'

export default class OrderEmailsController {
  public async find({ response }: HttpContextContract) {
    return response.ok(emailTemplates)
  }

  public async send({ request, response, params: { id } }: HttpContextContract) {
    const postSchema = schema.create({
      template_id: schema.enum(emailTemplates.map((e) => e.id.toString())),
    })

    try {
      const payload = await request.validate({ schema: postSchema })
      const template = emailTemplates.find((e) => e.id.toString() == payload.template_id)
      if (!template) {
        return response.badRequest({ error: 'template not found' })
      }
      const result = await CustomerNotificationEmail(id, template)
      return response.ok({ data: result, success: true })
    } catch (e) {
      console.log('notification email error :', e)
      return response.badRequest(e)
    }
  }

  public async sendPreview({ request, response, params: { id } }: HttpContextContract) {
    const postSchema = schema.create({
      template_id: schema.enum(emailTemplates.map((e) => e.id.toString())),
      email: schema.string({}, [rules.email()]),
    })

    try {
      const payload = await request.validate({ schema: postSchema })
      const template = emailTemplates.find((e) => e.id.toString() == payload.template_id)
      if (!template) {
        return response.badRequest({ error: 'template not found' })
      }
      const result = await CustomerNotificationEmail(id, template, payload.email)
      return response.ok({ data: result, success: true })
    } catch (e) {
      console.log('preview email error :', e)
      return response.badRequest(e)
    }
  }

  // public async confirmationMail({ response, params: { id } }: HttpContextContract) {
  //   const order = await Order.query().where('id', id).first()
  //   if (!order) {
  //     return response.notFound('order not found')
  //   }
  //   if (order.confirmationMailSentAt) {
  //     return response.badRequest({ message: 'email was already sent' })
  //   }
  //   try {
  //     const result = await CustomerNotificationEmail(id, CustomerNotifiationEmailType.CONFIRMATION)
  //     return response.ok({ data: result, success: true })
  //   } catch (e) {
  //     console.log(e)
  //     return response.internalServerError(e)
  //   }
  // }

  // public async deliveryEmail({ response, params: { id } }: HttpContextContract) {
  //   const order = await Order.query().where('id', id).preload('fulfillments').first()
  //   if (!order) {
  //     return response.notFound('order not found')
  //   }
  //   if (order.deliveryMailSentAt) {
  //     return response.badRequest({ message: 'email was already sent' })
  //   }
  //   if (order.fulfillments.length === 0) {
  //     return response.badRequest({ message: 'not active fulfillment for this order' })
  //   }
  //   try {
  //     const result = await CustomerNotificationEmail(id, CustomerNotifiationEmailType.DELIVERY)
  //     return response.ok({ data: result, success: true })
  //   } catch (e) {
  //     console.log(e)
  //     return response.internalServerError(e)
  //   }
  // }
}
