import Database from '@ioc:Adonis/Lucid/Database'
import { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import Event from 'App/Models/Event'
import { schema } from '@ioc:Adonis/Core/Validator'
import EventType from 'App/Models/EventType'
// import { adjustShopifyInventory } from 'App/Modules/ShopifyProductServices'

export default class EventsController {
  public async find({ request, response }: HttpContextContract) {
    const page = request.input('page', 1)
    const limit = request.input('limit', 20)
    const sort = request.input('sort', 'id:desc').split(':')

    const event = await Event.filter(request.all())
      .preload('status')
      .preload('eventType')
      .preload('stock', (query) => {
        query
          .preload('variant', (query) => {
            query.preload('product')
          })
          .preload('warehouse')
          .preload('quality')
      })
      .orderBy(sort[0], sort[1])
      .paginate(page, limit)
    return response.ok(event)
  }

  public async findOne({ response, params: { id } }: HttpContextContract) {
    const event = await await Event.query()
      .where('id', id)
      .preload('source')
      .preload('status')
      .preload('eventType')
      .preload('stock')
      .first()
    if (event != null) {
      return response.ok(event)
    }
    return response.notFound()
  }

  public async getTypes({ response }: HttpContextContract) {
    const eventTypes = await EventType.all()
    return response.ok(eventTypes)
  }

  public async create({ request, response }: HttpContextContract) {
    const postSchema = schema.create({
      stock_id: schema.number(),
      quantity: schema.number(),
      event_type_id: schema.number(),
      status_id: schema.number(),
      remark: schema.string.optional({ trim: true }),
      //TODO: remove this from model and database
      source_id: schema.number.optional(),
      from: schema.string.optional({ trim: true }),
      to: schema.string.optional({ trim: true }),
    })

    try {
      //use transaction to ensure that the databse
      //is synchronized with shopify
      const newEvent = await Database.transaction(async (trx) => {
        const payload = await request.validate({ schema: postSchema })
        const newEvent = await Event.create(payload, { client: trx })

        //if the status is 'completed' sync all the values on the other platforms
        // if (payload.status_id == 1) {
        // const updatedStock = await Stock.query()
        //   .where('id', payload.stock_id)
        //   .preload('variant', (query) => {
        //     query.preload('product')
        //   })

        //shopify
        // if (
        //   updatedStock[0].variant.shopifyId != '' &&
        //   updatedStock[0].variant.shopifyId != null &&
        //   updatedStock[0].variant.product.shopifySync == true
        // ) {
        //   await adjustShopifyInventory(updatedStock[0].variant.shopifyId, payload.quantity)
        // }
        // }
        return newEvent
      })
      return response.created({ data: newEvent, success: true })
    } catch (e) {
      return response.internalServerError(e)
    }
  }

  public async update({ request, response, params: { id } }: HttpContextContract) {
    const postSchema = schema.create({
      //quantity: schema.number.optional(),
      event_type_id: schema.number.optional(),
      status_id: schema.number.optional(),
      remark: schema.string.optional({ trim: true }),
      //TODO: remove this from model and database
      source_id: schema.number.optional(),
      from: schema.string.optional({ trim: true }),
      to: schema.string.optional({ trim: true }),
    })

    try {
      const payload = await request.validate({ schema: postSchema })

      const event = Database.transaction(async (trx) => {
        const event = await Event.query()
          .where('id', id)
          .preload('stock', (query) => {
            query.preload('variant', (query) => {
              query.preload('product')
            })
          })
          .first()

        if (event != null) {
          event.merge(payload)
          await event.useTransaction(trx).save()

          // if (
          //   event.statusId == 1 &&
          //   event.stock.variant.shopifyId != null &&
          //   event.stock.variant.product.shopifySync == true
          // ) {
          //   await adjustShopifyInventory(event.stock.variant.shopifyId, event.quantity)
          // }
          return event
        }

        return null
      })

      return event ? response.ok({ data: event, success: true }) : response.notFound()
    } catch (e) {
      return response.badRequest(e)
    }
  }
}
