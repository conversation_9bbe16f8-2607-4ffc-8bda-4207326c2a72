import {
  createShopifyProduct,
  createShopifyVariant,
  importProductsFromShopify,
  syncShopify,
} from 'App/Modules/ShopifyProductServices'
import { schema } from '@ioc:Adonis/Core/Validator'
import { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import ProductVariant from 'App/Models/ProductVariant'
import Stock from 'App/Models/Stock'
import Event from 'App/Models/Event'
import Product from 'App/Models/Product'
import Database from '@ioc:Adonis/Lucid/Database'
import shopifyClient from 'App/Network/shopifyClient'

export default class ProductsController {
  public async find({ request, response }: HttpContextContract) {
    const page = request.input('page', 1)
    const limit = request.input('limit', 20)
    const sort = request.input('sort', 'id:desc').split(':')
    const products = await Product
      //.where('was_deleted', false)
      .filter(request.all())
      .preload('variants')
      .orderBy(sort[0], sort[1])
      .paginate(page, limit)

    return response.ok(products)
  }

  public async findOne({ response, params: { id } }: HttpContextContract) {
    try {
      const product = await Product.query()
        .where('id', id)
        .preload('variants', (query) => {
          query.preload('stocks', (query) => {
            query
              .preload('warehouse')
              .preload('quality')
              .select('*')
              .select(
                Database.from('events')
                  .whereColumn('events.stock_id', 'stocks.id')
                  .andWhere('events.status_id', '1')
                  .sum('events.quantity')
                  .as('total_stocks')
                  .groupBy('stocks.id')
              )
          })
        })
        .first()
      if (product == null) {
        return response.notFound()
      }

      return response.ok(product)
    } catch (e) {
      console.log('ProductsController.findOne', e)
      return response.internalServerError(e)
    }
  }

  public async create({ request, response }: HttpContextContract) {
    const postSchema = schema.create({
      product_name: schema.string({ trim: true }),
      shopify_id: schema.string.optional({ trim: true }),
      sku: schema.string({ trim: true }),
      short_description: schema.string.optional({ trim: true }),
      shopify_sync: schema.boolean(),
      option_1: schema.string.optional({ trim: true }),
      option_2: schema.string.optional({ trim: true }),
      option_3: schema.string.optional({ trim: true }),
    })

    try {
      const payload = await request.validate({ schema: postSchema })

      const newProduct = await Product.create({
        productName: payload.product_name,
        shopifyId: payload.shopify_id ?? '',
        sku: payload.sku,
        shortDescription: payload.short_description ?? '',
      })

      await Database.transaction(async (trx) => {
        newProduct.shopifySync = payload.shopify_sync
        await newProduct.useTransaction(trx).save()

        if (payload.shopify_sync) {
          await createShopifyProduct(newProduct)
        }
      })

      return response.created({ data: newProduct, success: true })
    } catch (e) {
      console.log('ProductsController.create', e.messages)
      return response.badRequest(e)
    }
  }

  public async update({ request, response, params: { id } }: HttpContextContract) {
    const product = await Product.find(id)

    const postSchema = schema.create({
      product_name: schema.string.optional({ trim: true }),
      // shopify_id: schema.string.optional({ trim: true }),
      sku: schema.string.optional({ trim: true }),
      short_description: schema.string.optional({ trim: true }),
      option_1: schema.string.optional({ trim: true }),
      option_2: schema.string.optional({ trim: true }),
      option_3: schema.string.optional({ trim: true }),
    })
    //TODO: perform check for options, if any of the variants have option2 or 3,
    //the corresponding options from the product must not be removed/left empty
    if (product != null) {
      try {
        const payload = await request.validate({ schema: postSchema })
        product.merge(payload)
        await product.save()
        return response.ok({ data: product, success: true })
      } catch (e) {
        console.log(e)
        return response.badRequest(e)
      }
    }
    return response.notFound()
  }

  public async createVariant({ request, response, params: { id } }: HttpContextContract) {
    const product = await Product.find(id)

    if (product != null) {
      const postSchema = schema.create({
        variant_name: schema.string.optional({ trim: true }),
        sku: schema.string({ trim: true }),
        shopify_id: schema.string.optional({ trim: true }),
        initial_stock: schema.number(),
        warehouse_id: schema.number(),
        option_1: schema.string({ trim: true }),
        option_2: schema.string.optional({ trim: true }),
        option_3: schema.string.optional({ trim: true }),
        price: schema.number.optional(),
        weight: schema.number.optional(),
        barcode: schema.string.optional({ trim: true }),
        availability: schema.boolean.optional(),
      })

      try {
        const payload = await request.validate({ schema: postSchema })
        if (payload.initial_stock < 0) {
          return response.badRequest({ message: 'initial_stock cannot be lower that 0' })
        }

        const newVariant = await Database.transaction(async (trx) => {
          const newVariant = await ProductVariant.create(
            {
              productId: id,
              variantName:
                payload.variant_name ??
                payload.option_1 +
                  (payload.option_2 ? ' / ' + payload.option_2 : '') +
                  (payload.option_3 ? payload.option_3 + ' / ' : ''),
              sku: payload.sku,
              shopifySku: product.shopifySync ? payload.sku : '',
              shopifyId: payload.shopify_id ?? '',
              option1: payload.option_1 ?? '',
              option2: payload.option_2 ?? '',
              option3: payload.option_3 ?? '',
              price: payload.price,
              weight: payload.weight,
            },
            {
              client: trx,
            }
          )

          const newStock = await Stock.create(
            {
              warehouseId: payload.warehouse_id,
              variantId: newVariant.id,
            },
            {
              client: trx,
            }
          )

          if (payload.initial_stock > 0) {
            await Event.create(
              {
                stockId: newStock.id,
                quantity: payload.initial_stock,
                eventTypeId: 1,
                statusId: 1,
                sourceId: 1,
              },
              {
                client: trx,
              }
            )
          }

          return newVariant
        })

        if (product.shopifySync) {
          await createShopifyVariant(product.shopifyId, newVariant, payload.initial_stock)
        }

        if (product.amazonSync) {
        }

        return response.created({ data: newVariant, success: true })
      } catch (e) {
        return response.badRequest(e)
      }
    }
    return response.notFound()
  }

  public async syncShopify({ request, response }: HttpContextContract) {
    const ids: any[] = request.input('ids')
    const sync: boolean = request.input('sync')
    if (ids !== null && sync !== null) {
      const updatedProducts = await Product.query()
        .whereIn('id', ids)
        .andWhere('shopify_sync', !sync)
      //if the syncing is reactivated, call shopify api
      //to update all the inventory levels
      for (var i = 0; i < updatedProducts.length; i++) {
        await Database.transaction(async (trx) => {
          updatedProducts[i].shopifySync = sync
          await updatedProducts[i].useTransaction(trx).save()

          if (sync) {
            //if the same product exists on shopify, sync them
            //otherwise, create the product on shopify, then sync them
            if (updatedProducts[i].shopifyId) {
              const findProduct = await shopifyClient.get(
                `/products/${updatedProducts[i].shopifyId}.json`
              )
              const variants = findProduct.data['product']['variants']
              await syncShopify(variants)
            } else {
            }
          }
        })
      }

      return response.ok(updatedProducts)
    }
    return response.badRequest()
  }

  public async importFromShopify({ request, response }: HttpContextContract) {
    const shopifyProductIds: any[] = request.input('shopify_ids')
    const res = await shopifyClient.get(`/products.json`)
    const shopifyProducts: any[] = res.data['products']
    const selectedProducts: any[] = []
    shopifyProductIds.forEach((id) => {
      const product = shopifyProducts.find((product) => product['id'] == id)
      selectedProducts.push(product)
    })

    try {
      const updatedProducts = await importProductsFromShopify(selectedProducts)
      return response.ok(updatedProducts)
    } catch (e) {
      return response.internalServerError(e)
    }
  }

  public async findShopify({ response }: HttpContextContract) {
    try {
      const res = await shopifyClient.get('/products.json')
      const variants = await ProductVariant.all()
      const skus = variants.map((variant) => variant.sku)
      const shopifyProducts: any[] = res.data['products']
      const filteredShopifyProducts = shopifyProducts.filter((shopifyProduct) => {
        return shopifyProduct.variants.find((variant) => skus.includes(variant.sku)) === undefined
      })
      console.log
      return response.ok(filteredShopifyProducts)
    } catch (e) {
      console.log(e)
      return response.internalServerError(e)
    }
  }

  public async importFromCSV() {}
}
