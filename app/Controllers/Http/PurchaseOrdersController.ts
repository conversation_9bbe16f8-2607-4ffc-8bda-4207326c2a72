import { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import { rules, schema } from '@ioc:Adonis/Core/Validator'
import AppSetting from 'App/Models/AppSetting'
import PurchaseOrder, { PurchaseOrderStatus } from 'App/Models/PurchaseOrder'
import TrackingCompany from 'App/Models/TrackingCompany'
import { archiveFiles } from 'App/Modules/ArchiveFiles'
import {
  createPurchaseOrderByCsv,
  generatePoCSVData,
  updatePurchaseOrderTrackingByCsv,
} from 'App/Modules/PurchaseOrderService'
import _ from 'lodash'
import ObjectToCsv from 'node-create-csv'
import Fs from 'fs'
import { Exception } from '@adonisjs/core/build/standalone'
import ProductVariant from 'App/Models/ProductVariant'

export default class PurchaseOrdersController {
  public async find({ request, response }: HttpContextContract) {
    const page = request.input('page', 1)
    const limit = request.input('limit', 20)
    const sort = request.input('sort', 'created_at:desc').split(':')
    const result = await PurchaseOrder.filter(request.all())
      .leftJoin(
        'purchase_order_fulfillments',
        'purchase_order_fulfillments.purchase_order_id',
        'purchase_orders.id'
      )
      .leftJoin(
        'purchase_order_items',
        'purchase_order_items.purchase_order_id',
        'purchase_orders.id'
      )
      .preload('items', (query) =>
        query.preload('productVariant', (query) => query.preload('product'))
      )
      .preload('fulfillments', (query) => query.preload('status'))
      .select('purchase_orders.*')
      .groupBy('purchase_orders.id')
      .orderBy(sort[0], sort[1])
      .paginate(page, limit)

    return response.ok(result)
  }

  public async findOne({ response, params: { po_id } }: HttpContextContract) {
    const result = await PurchaseOrder.query()
      .where('id', po_id)
      .preload('items', (query) =>
        query.preload('productVariant', (query) => query.preload('product'))
      )
      .preload('fulfillments', (query) => query.preload('status'))
      .first()
    if (!result) {
      return response.notFound()
    }

    return response.ok(result)
  }

  public async findByTracking({ request, response }: HttpContextContract) {
    const trackingNumber = request.input('tracking_number')
    const trackingCompanyNames = request.input('tracking_company_names').split(',')
    if (!trackingNumber) {
      return response.badRequest({ message: 'tracking_number not specified' })
    }

    const po = await PurchaseOrder.query()
      .join(
        'purchase_order_fulfillments',
        'purchase_order_fulfillments.purchase_order_id',
        '=',
        'purchase_orders.id'
      )
      .where('purchase_order_fulfillments.tracking_number', trackingNumber)
      .andWhereIn('purchase_order_fulfillments.tracking_company_name', trackingCompanyNames)
      .preload('items', (query) =>
        query.preload('productVariant', (query) => query.preload('product'))
      )
      .preload('fulfillments', (query) => query.preload('status'))
      .first()

    return response.ok(po)
  }

  public async updateTrackingByCsv({ request, response }: HttpContextContract) {
    const postSchema = schema.create({
      csv: schema.file({
        extnames: ['csv', 'xlsx'],
      }),
      tracking_company_id: schema.number([
        rules.exists({
          table: 'tracking_companies',
          column: 'id',
        }),
      ]),
      id_column: schema.string.optional({}, [rules.inValues(['id', 'platform_order_id', 'name'])]),
    })

    try {
      const payload = await request.validate({ schema: postSchema })
      const trackingCompany = await TrackingCompany.findOrFail(payload.tracking_company_id)
      const file = payload.csv

      if (file.tmpPath != null) {
        const filePath: string = file.tmpPath
        const results = await updatePurchaseOrderTrackingByCsv(filePath, trackingCompany)
        return response.created({ data: results, success: true })
      }
      return response.badRequest({ error: 'missing csv' })
    } catch (e) {
      console.log(e)
      return response.badRequest(e)
    }
  }

  public async createByCsv({ request, response }: HttpContextContract) {
    const postSchema = schema.create({
      csv: schema.file({
        extnames: ['csv', 'xlsx'],
      }),
      remark: schema.string.optional(),
      receiver_email: schema.string(),
      first_name: schema.string(),
      last_name: schema.string.optional(),
      phone: schema.string(),
      zip: schema.string(),
      address: schema.string(),
      apartment: schema.string.optional(),
      company: schema.string.optional(),
    })

    const payload = await request.validate({ schema: postSchema })

    try {
      const file = payload.csv

      if (file.tmpPath != null) {
        const filePath: string = file.tmpPath
        let data = { ...payload } as any
        delete data['csv']
        const results = await createPurchaseOrderByCsv(filePath, data)
        return response.created({ data: results, success: true })
      }
      return response.badRequest({ error: 'missing csv' })
    } catch (e) {
      console.log(e)
      return response.badRequest(e)
    }
  }

  public async downloadShippngCsv({ request, response }: HttpContextContract) {
    const postSchema = schema.create({
      ignore_generated: schema.boolean.optional(),
      waiting_shipped_only: schema.boolean.optional(),
      generate_picking_list: schema.boolean.optional(),
      separate_order_limit: schema.number.optional([rules.range(1, 999)]),
      remove_jp_phone_code: schema.boolean.optional(),
      default_item_name: schema.string.optional({ trim: true }),
      purchase_order_ids: schema.array().members(schema.number()),
      tracking_company_id: schema.number([
        rules.exists({
          table: 'tracking_companies',
          column: 'id',
        }),
      ]),
      sort_by: schema.string.optional({}, [
        rules.inValues(['id', 'product_name', 'sku', 'platform_order_id', 'name', 'created_at']),
      ]),
      desc: schema.boolean.optional(),
      exclude_sku: schema.array.optional().members(schema.string()),
      full_exclude_sku: schema.boolean.optional(),
      include_sku: schema.array.optional().members(schema.string()),
      full_include_sku: schema.boolean.optional(),
    })

    try {
      const payload = await request.validate({ schema: postSchema })

      const trackingCompany = await TrackingCompany.find(payload.tracking_company_id)

      let poQuery = PurchaseOrder.query()

      if (payload.waiting_shipped_only) {
        poQuery = poQuery.where('purchase_orders.status', PurchaseOrderStatus.PENDING)
      }

      if (payload.purchase_order_ids && payload.purchase_order_ids.length > 0) {
        poQuery = poQuery.andWhereIn('purchase_orders.id', payload.purchase_order_ids)
      }

      //exclude certain sku
      if (
        payload.exclude_sku &&
        payload.exclude_sku.length > 0 &&
        payload.full_exclude_sku == true
      ) {
        poQuery = poQuery.whereNotIn('purchase_order_items.sku', payload.exclude_sku)
      }

      //include certain sku
      if (
        payload.include_sku &&
        payload.include_sku.length > 0 &&
        payload.full_include_sku == true
      ) {
        poQuery = poQuery.whereIn('purchase_order_items.sku', payload.include_sku)
      }

      //for product name and sku will be do in another way, since it involves aggregation
      if (payload.sort_by === 'id' || payload.sort_by === 'created_at') {
        const direction = payload.desc ? 'desc' : 'asc'
        poQuery = poQuery.orderBy(`purchase_orders.${payload.sort_by}`, direction)
      }

      var po: PurchaseOrder[]
      const appSetting = await AppSetting.query()
        .preload('fulfillmentSetting')
        .preload('yamatoSetting')
        .first()
      const yamatoSetting = appSetting?.yamatoSetting
      const fulfillmentSetting = appSetting?.fulfillmentSetting
      if (!yamatoSetting) {
        return response.internalServerError({
          message: 'Yamato settings could not be found',
        })
      }
      if (!fulfillmentSetting) {
        return response.internalServerError({
          message: 'Fulfillment settings could not be found',
        })
      }
      const filepaths: string[] = []
      const utc0Date = new Date()
      const date = new Date(utc0Date.getTime() + 9 * 60 * 60 * 1000)
      const dateArr = date.toISOString().split('T')[0].split('-')
      const dateStr = `${dateArr[0]}-${dateArr[1]}-${dateArr[2]}`

      po = await poQuery
        .leftJoin(
          'purchase_order_items',
          'purchase_order_items.purchase_order_id',
          '=',
          'purchase_orders.id'
        )
        .preload('items', (query) => {
          query.preload('productVariant', (query) => {
            query.preload('product')
          })
        })
        .groupBy('purchase_orders.id')
        .select('purchase_orders.*')

      if (po.length < 1) {
        return response.notFound({ message: 'No records were found' })
      }

      if (payload.generate_picking_list) {
        const pickingListFilePaths = await createPoPickingList(
          // payload.separate_order_limit,
          payload.waiting_shipped_only,
          po.map((item) => item.id)
        )
        filepaths.push(...pickingListFilePaths)
      }

      if (payload.sort_by === 'product_name') {
        po = po.sort((a, b) => {
          if (!a.items[0] || !b.items[0]) {
            return 0
          }
          const fullnameA = a.items[0]?.productVariant.fullname
          const fullnameB = b.items[0]?.productVariant.fullname
          if (payload.desc) {
            return fullnameA.localeCompare(fullnameB)
          }
          return fullnameB.localeCompare(fullnameA)
        })
      }

      if (payload.sort_by === 'sku') {
        po = po.sort((a, b) => {
          if (!a.items[0] || !b.items[0]) {
            return 0
          }
          const skuA = a.items[0]?.sku
          const skuB = b.items[0]?.sku
          if (!skuA || !skuB) {
            return 0
          }
          if (payload.desc) {
            return skuA.localeCompare(skuB)
          }
          return skuB.localeCompare(skuA)
        })
      }

      const dataCmb = await generatePoCSVData({
        purchaseOrders: po,
        trackingCompany: trackingCompany!,
        itemName: payload.default_item_name ?? '',
        removeJpPhoneCode: payload.remove_jp_phone_code,
      })

      await new ObjectToCsv(dataCmb).toDisk(`${process.env.CSV_DIR}/po-${dateStr}.csv`, {
        showHeader: true,
      }) //save to disk
      filepaths.push(`po-${dateStr}.csv`)
      const compressedFilePath = await archiveFiles(filepaths, dateStr)

      response.stream(Fs.createReadStream(compressedFilePath))
    } catch (e) {
      console.log(e)
      return response.badRequest(e)
    }
  }

  public async update({ response, request, params: { po_id } }: HttpContextContract) {
    const po = await PurchaseOrder.query().where('id', po_id).first()
    if (!po) {
      return response.notFound()
    }

    const postSchema = schema.create({
      remark: schema.string.optional(),
      receiver_email: schema.string(),
      first_name: schema.string(),
      last_name: schema.string.optional(),
      phone: schema.string.optional(),
      zip: schema.string.optional(),
      address: schema.string(),
      apartment: schema.string.optional(),
      company: schema.string.optional(),
      status: schema.enum(Object.values(PurchaseOrderStatus)),
    })

    const payload = await request.validate({ schema: postSchema })
    po.merge({
      remark: payload.remark ?? '',
      receiverEmail: payload.receiver_email,
      firstName: payload.first_name,
      lastName: payload.last_name,
      phone: payload.phone,
      zip: payload.zip,
      address: payload.address,
      apartment: payload.apartment ?? '',
      company: payload.company ?? '',
      status: payload.status,
    })

    await po.save()

    return response.ok({ success: true, data: po })
  }
}

export async function createPoPickingList(waitingShippedOnly: boolean = false, ids: number[] = []) {
  var poItemQuery = ProductVariant.query()

  if (waitingShippedOnly) {
    poItemQuery = poItemQuery.andWhere('purchase_orders.status', PurchaseOrderStatus.PENDING)
  }

  if (ids && ids.length > 0) {
    poItemQuery = poItemQuery.andWhereIn('purchase_orders.id', ids)
  }

  const appSetting = await AppSetting.query()
    .preload('fulfillmentSetting')
    .preload('yamatoSetting')
    .first()

  const yamatoSetting = appSetting?.yamatoSetting
  const fulfillmentSetting = appSetting?.fulfillmentSetting
  if (!yamatoSetting) {
    throw new Exception('Yamato settings could not be found')
  }
  if (!fulfillmentSetting) {
    throw new Exception('Fulfillment settings could not be found')
  }
  const filepaths: string[] = []
  const ungroupedData: any[] = []
  const date = new Date()
  const dateArr = date.toISOString().split('T')[0].split('-')
  const dateStr = `${dateArr[0]}-${dateArr[1]}-${dateArr[2]}`

  const pickingList = await poItemQuery
    .join('purchase_order_items', 'purchase_order_items.variant_id', '=', 'product_variants.id')
    .join('purchase_orders', 'purchase_order_items.purchase_order_id', '=', 'purchase_orders.id')
    .preload('product')
    .sum('purchase_order_items.qty as total_number')
    .select('product_variants.*')
    .groupBy('product_variants.id')
    .orderBy('product_variants.sku')

  if (pickingList.length < 1) {
    return []
  }

  for (const pickingItem of pickingList) {
    let data = {
      品名: pickingItem.fullname,
      SKU: pickingItem.sku,
      数: pickingItem.$extras['total_number'],
    }

    console.log(data)

    ungroupedData.push(data)
  }

  await new ObjectToCsv(ungroupedData).toDisk(
    `${process.env.CSV_DIR}/${dateStr}-picking-list.csv`,
    {
      showHeader: true,
    }
  ) //save to disk

  filepaths.push(`${dateStr}-picking-list.csv`)
  // }

  return filepaths
}
