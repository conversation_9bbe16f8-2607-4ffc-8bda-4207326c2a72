import { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import { schema } from '@ioc:Adonis/Core/Validator'
import AppSetting from 'App/Models/AppSetting'
import FulfillmentSetting from 'App/Models/FulfillmentSetting'

export default class FulfillmentSettingsController {
  public async find({ request, response }: HttpContextContract) {
    const page = request.input('page', 1)
    const limit = request.input('limit', 20)
    const sort = request.input('sort', 'id:desc').split(':')

    const fulfillmentSetting = await FulfillmentSetting.query()
      .preload('appSetting')
      .orderBy(sort[0], sort[1])
      .paginate(page, limit)
    return response.ok(fulfillmentSetting)
  }

  public async findOne({ response, params: { id } }: HttpContextContract) {
    const result = await FulfillmentSetting.query().where('id', id).preload('appSetting').first()
    if (result == null) {
      return response.notFound()
    }

    return response.ok(result)
  }

  public async create({ request, response }: HttpContextContract) {
    const postSchema = schema.create({
      setting_name: schema.string({ trim: true }),
      default_item_name: schema.string({ trim: true }),
      name: schema.string({ trim: true }),
      phone: schema.string({ trim: true }),
      postal: schema.string({ trim: true }),
      province: schema.string({ trim: true }),
      city: schema.string({ trim: true }),
      address_1: schema.string({ trim: true }),
      address_2: schema.string.optional({ trim: true }),
    })
    const isDefault = request.input('is_default')

    try {
      const payload = await request.validate({
        schema: postSchema,
      })

      const createFulfillmentSetting = await FulfillmentSetting.create(payload)
      if (isDefault) {
        const appSetting = await AppSetting.first()
        if (appSetting == null) {
          return response.notFound()
        }
        try {
          appSetting.fulfillmentSettingId = createFulfillmentSetting.id
          await appSetting.save()
        } catch (e) {
          return response.badRequest(e)
        }
      }

      response.created({ data: createFulfillmentSetting, success: true })
    } catch (e) {
      return response.internalServerError(e)
    }
  }

  public async update({ request, response, params: { id } }: HttpContextContract) {
    const fulfillmentSetting = await FulfillmentSetting.find(id)
    if (fulfillmentSetting == null) {
      return response.notFound()
    }

    const updateSchema = schema.create({
      setting_name: schema.string({ trim: true }),
      default_item_name: schema.string({ trim: true }),
      name: schema.string({ trim: true }),
      phone: schema.string({ trim: true }),
      postal: schema.string({ trim: true }),
      province: schema.string({ trim: true }),
      city: schema.string({ trim: true }),
      address_1: schema.string({ trim: true }),
      address_2: schema.string.optional({ trim: true }),
    })
    const isDefault = request.input('is_default')

    try {
      const payload = await request.validate({ schema: updateSchema })

      fulfillmentSetting.merge(payload)
      await fulfillmentSetting.save()

      if (isDefault) {
        const appSetting = await AppSetting.first()
        if (appSetting == null) {
          return response.notFound()
        }
        try {
          appSetting.fulfillmentSettingId = fulfillmentSetting.id
          await appSetting.save()
        } catch (e) {
          return response.badRequest(e)
        }
      }

      return response.ok({ data: fulfillmentSetting, success: true })
    } catch (e) {
      console.log(e)
      return response.badRequest(e)
    }
  }

  public async delete({ response, params: { id } }: HttpContextContract) {
    const fulfillmentSetting = await FulfillmentSetting.find(id)

    if (fulfillmentSetting == null) {
      return response.notFound()
    }

    if (fulfillmentSetting.isDefault) {
      return response.badRequest({ message: 'Unable to delete default fulfillment setting ' })
    }

    try {
      await fulfillmentSetting.delete()
      return response.ok({ data: fulfillmentSetting, success: true })
    } catch (e) {
      return response.badRequest(e)
    }
  }
}
