import Customer from 'App/Models/Customer'
import { schema, rules } from '@ioc:Adonis/Core/Validator'
import { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import Review from 'App/Models/Review'
import Product from 'App/Models/Product'
import axios from 'axios'

export default class ReviewsController {
  public async find({ request, response }: HttpContextContract) {
    const page = request.input('page', 1)
    const limit = request.input('limit', 20)
    const sort = request.input('sort', 'id:desc').split(':')
    const reviews = await Review.filter(request.all())
      .where('was_deleted', false)
      .preload('customer')
      .preload('product')
      .orderBy(sort[0], sort[1])
      .paginate(page, limit)

    return response.ok(reviews)
  }

  public async findOverview({ request, response }: HttpContextContract) {
    const product = await Product.filter(request.all())
      .withAggregate('reviews', (query) => {
        query.max('score').as('max_rating')
      })
      .withAggregate('reviews', (query) => {
        query.min('score').as('min_rating')
      })
      .withAggregate('reviews', (query) => {
        query.avg('score').as('avg_rating')
      })
      .withAggregate('reviews', (query) => {
        query.count('score').as('rating_count')
      })
      .firstOrFail()

    return response.ok(product)
  }

  public async findOne({ response, params: { id } }: HttpContextContract) {
    const review = await Review.query().where('id', id).andWhere('was_deleted', false).first()
    return response.ok(review)
  }

  public async findWithProductId({ response, params: { product_id } }: HttpContextContract) {
    const review = await Review.query()
      .where('product_id', product_id)
      .andWhere('was_deleted', false)
      .first()
    return response.ok(review)
  }

  //should verify purchase?
  public async create({ request, response, params: { product_id }, auth }: HttpContextContract) {
    const user = auth.user

    const postSchema = schema.create({
      title: schema.string.optional({ trim: true }),
      content: schema.string.optional({ trim: true }),
      score: schema.number([rules.range(0, 5)]),
    })

    if (user?.id) {
      try {
        const payload = await request.validate({ schema: postSchema })
        const product = await Product.findOrFail(product_id)
        const customer = await Customer.query().where('user_id', user.id).firstOrFail()
        const newReview = await Review.create({
          title: payload.title ?? '',
          content: payload.content ?? '',
          score: payload.score,
          productId: product_id,
          shopifyId: product.shopifyId,
          // TODO: to be confirmed
          customerId: customer.id,
        })

        return response.created({ data: newReview, success: true })
      } catch (e) {
        return response.badRequest(e)
      }
    }
  }

  //TODO: should verify purchase?
  public async emailReview({ request, response, params: { product_id } }: HttpContextContract) {
    const customer = await Customer.firstOrCreate({
      email: request.input('email'),
    })

    const postSchema = schema.create({
      title: schema.string.optional({ trim: true }),
      content: schema.string.optional({ trim: true }),
      score: schema.number([rules.range(0, 5)]),
    })

    try {
      const payload = await request.validate({ schema: postSchema })
      const product = await Product.findOrFail(product_id)
      const newReview = await Review.create({
        title: payload.title ?? '',
        content: payload.content ?? '',
        score: payload.score,
        productId: product_id,
        shopifyId: product.shopifyId,
        // TODO: to be confirmed
        customerId: customer.id,
      })

      return response.created(newReview)
    } catch (e) {
      return response.badRequest(e)
    }
  }

  public async createByShopifyId({ request, response, auth }: HttpContextContract) {
    const user = auth.user
    const postSchema = schema.create({
      title: schema.string.optional({ trim: true }),
      content: schema.string.optional({ trim: true }),
      score: schema.number([rules.range(0, 5)]),
      shopify_id: schema.string(),
    })

    try {
      const payload = await request.validate({ schema: postSchema })
      const product = await Product.findByOrFail('shopify_id', payload.shopify_id)
      const newReview = await Review.create({
        title: payload.title ?? '',
        content: payload.content ?? '',
        score: payload.score,
        productId: product.id,
        shopifyId: product.shopifyId,
        // TODO: to be confirmed
        customerId: user?.id,
      })

      return response.created(newReview)
    } catch (e) {
      return response.badRequest(e)
    }
  }

  public async manualCreate({ request, response }: HttpContextContract) {
    const postSchema = schema.create({
      title: schema.string.optional({ trim: true }),
      content: schema.string.optional({ trim: true }),
      score: schema.number([rules.range(0, 5)]),
      product_id: schema.number([
        rules.unsigned(),
        rules.exists({
          table: 'products',
          column: 'id',
        }),
      ]),
      customer_id: schema.number([
        rules.unsigned(),
        rules.exists({
          table: 'customers',
          column: 'id',
        }),
      ]),
      order_date: schema.date(),
      published: schema.boolean.optional(),
    })

    try {
      const payload = await request.validate({ schema: postSchema })
      const product = await Product.findOrFail(payload.product_id)
      const newReview = await Review.create({
        title: payload.title ?? '',
        content: payload.content ?? '',
        score: payload.score,
        productId: payload.product_id,
        shopifyId: product.shopifyId,
        customerId: payload.customer_id,
        orderDate: payload.order_date,
        published: payload.published ?? false,
      })

      return response.created({ data: newReview, success: true })
    } catch (e) {
      console.log(e)
      return response.badRequest(e)
    }
  }

  // TODO: update should only be available to admins?
  public async manualUpdate({ request, response, params: { id } }: HttpContextContract) {
    const postSchema = schema.create({
      title: schema.string.optional({ trim: true }),
      content: schema.string.optional({ trim: true }),
      score: schema.number.optional([rules.range(0, 5)]),
      votes_up: schema.number.optional([rules.unsigned()]),
      votes_down: schema.number.optional([rules.unsigned()]),
      product_id: schema.number.optional([rules.unsigned()]),
      customer_id: schema.number.optional([rules.unsigned()]),
      order_date: schema.date.optional(),
      shopify_id: schema.number.optional(),
      published: schema.boolean.optional(),
    })

    const review = await Review.findOrFail(id)
    const payload = await request.validate({ schema: postSchema })
    const product = await Product.findOrFail(payload.product_id)

    review.merge(payload)
    review.shopifyId = product.shopifyId
    await review.save()

    return response.ok({ data: review, success: true })
  }

  // TODO: update for users should be disabled
  public async update({ request, response, params: { id }, auth }: HttpContextContract) {
    const userId = auth.user?.id
    if (userId != null) {
      const review = await Review.query()
        .where('id', id)
        .andWhere('user_id', userId)
        .andWhere('was_deleted', false)
        .first()

      if (review != null) {
        const postSchema = schema.create({
          title: schema.string.optional({ trim: true }),
          content: schema.string.optional({ trim: true }),
          score: schema.number.optional([rules.range(0, 5)]),
          votes_up: schema.number.optional([rules.unsigned()]),
          votes_down: schema.number.optional([rules.unsigned()]),
        })

        const payload = await request.validate({ schema: postSchema })
        review.merge(payload)
        await review.save()

        return response.ok({ data: review, success: true })
      }
    }
    return response.badRequest()
  }

  public async delete({ response, params: { id } }: HttpContextContract) {
    const review = await Review.query().where('id', id).andWhere('was_deleted', false).first()
    if (review != null) {
      review.wasDeleted = true
      await review.save()
      return response.ok(review)
    }
    return response.notFound()
  }

  public async subscribeToKlaviyo({ request, response }: HttpContextContract) {
    const email = request.input('email')

    try {
      const klaviyoResponse = await axios.post(
        `https://a.klaviyo.com/api/v2/list/SJKzKq/members?api_key=*************************************`,
        {
          profiles: [
            {
              email,
            },
          ],
        }
      )

      console.log(klaviyoResponse)

      return response.ok({
        success: true,
      })
    } catch (err) {
      return response.badRequest(err)
    }
  }
}
