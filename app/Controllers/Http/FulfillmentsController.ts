import { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import { rules, schema } from '@ioc:Adonis/Core/Validator'
import Fulfillment from 'App/Models/Fulfillment'
import { fulfillShopifyOrder } from 'App/Modules/ShopifyOrderServices'
import { WeightBasedShippingService } from 'App/Modules/WeightBasedShippingService'

export default class FulfillmentsController {
  public async find({ request, response }: HttpContextContract) {
    const page = request.input('page', 1)
    const limit = request.input('limit', 20)
    const sort = request.input('sort', 'created_at:desc').split(':')
    const result = await Fulfillment.filter(request.all())
      .preload('status')
      .preload('order', (query) => {
        query.preload('shopPlatform')
        query.preload('status')
        query.preload('customer')
        query.preload('lineItems', (query) => {
          query.preload('productVariant')
          query.preload('fulfillmentLineItems', (query) => {
            query.preload('event')
          })
        })
      })
      .preload('fulfillmentLineItems', (query) => {
        query.preload('lineItem')
        query.preload('event')
      })
      .orderBy(sort[0], sort[1])
      .paginate(page, limit)

    return response.ok(result)
  }

  public async findOne({ response, params: { id } }: HttpContextContract) {
    const fulfillment = await Fulfillment.query()
      .where('id', id)
      .preload('status')
      .preload('order', (query) => {
        query.preload('shopPlatform')
        query.preload('status')
        query.preload('customer')
        query.preload('lineItems', (query) => {
          query.preload('productVariant')
          query.preload('fulfillmentLineItems', (query) => {
            query.preload('event')
          })
        })
      })
      .preload('fulfillmentLineItems', (query) => {
        query.preload('lineItem')
        query.preload('event')
      })
      .first()

    if (!fulfillment) {
      return response.notFound()
    }

    return response.ok(fulfillment)
  }

  public async findByTracking({ request, response }: HttpContextContract) {
    const trackingNumber = request.input('tracking_number')
    const trackingCompanyNames = request.input('tracking_company_names').split(',')
    if (!trackingNumber) {
      return response.badRequest({ message: 'tracking_number not specified' })
    }

    const fulfillment = await Fulfillment.query()
      .where('tracking_number', trackingNumber)
      .andWhereIn('tracking_company_name', trackingCompanyNames)
      .preload('status')
      .preload('order', (query) => {
        query.preload('shopPlatform')
        query.preload('status')
        query.preload('customer')
        query.preload('lineItems', (query) => {
          query.preload('productVariant')
          query.preload('fulfillmentLineItems', (query) => {
            query.preload('event')
          })
        })
      })

    return response.ok(fulfillment)
  }

  public async create({ request, response }: HttpContextContract) {
    const postSchema = schema.create({
      order_id: schema.number([
        rules.exists({
          table: 'orders',
          column: 'id',
        }),
      ]),
      tracking_number: schema.string.optional(),
      tracking_company_name: schema.string.optional(),
      service: schema.string.optional(),
    })

    try {
      const payload = await request.validate({ schema: postSchema })

      // Get order with line items to calculate weight
      const order = await Fulfillment.query()
        .where('id', payload.order_id)
        .preload('order', (query) => {
          query.preload('lineItems')
        })
        .first()

      if (!order) {
        return response.notFound({ message: 'Order not found' })
      }

      // Use weight-based shipping to determine tracking company
      let trackingCompanyName = payload.tracking_company_name
      if (!trackingCompanyName) {
        const weightResult = await WeightBasedShippingService.getTrackingCompanyForOrder(
          order.order
        )
        trackingCompanyName = weightResult.trackingCompany.name
      }

      const fulfillment = await Fulfillment.create({
        orderId: payload.order_id,
        trackingNumber: payload.tracking_number,
        trackingCompanyName: trackingCompanyName,
        service: payload.service,
        fulfillmentStatusId: 1, // pending
      })

      await fulfillment.load('order')
      await fulfillment.load('status')

      return response.created(fulfillment)
    } catch (e) {
      console.log('create fulfillment', e)
      return response.badRequest(e)
    }
  }

  public async fulfill({ request, response, params: { id } }: HttpContextContract) {
    const postSchema = schema.create({
      notify_customer: schema.boolean.optional(),
      line_items: schema.array([rules.minLength(1)]).members(
        schema.object().members({
          line_item_id: schema.number([
            rules.exists({
              table: 'line_items',
              column: 'id',
            }),
          ]),
          //TODO: make this optional
          quantity: schema.number([rules.range(1, 999)]),
        })
      ),
    })

    try {
      const payload = await request.validate({ schema: postSchema })

      const fulfillment = await Fulfillment.query()
        .where('id', id)
        .preload('order', (query) => {
          query.preload('lineItems', (query) => {
            query.preload('fulfillmentLineItems', (query) => {
              query.preload('event')
            })
          })
        })
        .first()

      if (fulfillment == null) {
        return response.notFound()
      }

      const order = fulfillment.order

      //check if fulfillment was completed
      if (fulfillment.fulfillmentStatusId == 2) {
        return response.unprocessableEntity({ message: 'this fulfillment was already completed' })
      }

      //check duplicate line_item_id
      const valueArr = payload.line_items.map((item) => item.line_item_id)
      const isDuplicate = valueArr.some((item, idx) => valueArr.indexOf(item) != idx)
      if (isDuplicate) {
        return response.badRequest({ message: 'duplicated line_item_id detected' })
      }

      //check if line_items match the requirements
      for (var i = 0; i < payload.line_items.length; i++) {
        const line_item = payload.line_items[i]
        const propertyName = 'line_items.' + i
        const orderLineItem = order.lineItems.find((item) => item.id === line_item.line_item_id)
        if (!orderLineItem) {
          var res: any = {}
          res[propertyName] = [{ line_item_id: 'invalid line_item_id' }]
          return response.badRequest(res)
        }

        if (
          line_item.quantity <= 0 ||
          line_item.quantity > (orderLineItem.fulfillable_quantity ?? 0)
        ) {
          var res: any = {}
          res[propertyName] = [{ quantity: 'invalid quantity' }]
          return response.badRequest(res)
        }
      }

      //check if order was fulfilled
      if (order.orderStatusId === 5 || order.orderStatusId === 7) {
        return response.unprocessableEntity({ message: 'this order has been closed' })
      }

      // If no tracking company is set, use weight-based selection
      if (!fulfillment.trackingCompanyName) {
        const weightResult = await WeightBasedShippingService.getTrackingCompanyForOrder(order)
        fulfillment.trackingCompanyName = weightResult.trackingCompany.name
        await fulfillment.save()
      }

      if (!fulfillment.trackingNumber) {
        return response.internalServerError({
          message: 'this fulfillment does not have a tracking code',
        })
      }

      const result = await fulfillShopifyOrder(
        fulfillment.id,
        payload.line_items,
        payload.notify_customer
      )

      //update order status to shipped

      return response.ok({ data: result, success: true })
    } catch (e) {
      console.log('order fufillment', e)
      return response.badRequest(e)
    }
  }
}