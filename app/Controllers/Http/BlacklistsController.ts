import { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import { schema, rules } from '@ioc:Adonis/Core/Validator'
import Blacklist from 'App/Models/Blacklist'

export default class BlacklistsController {
  public async find({ request, response }: HttpContextContract) {
    const page = request.input('page', 1)
    const limit = request.input('limit', 20)
    const sort = request.input('sort', 'id:desc').split(':')
    const result = await Blacklist.filter(request.all())
      .preload('customer')
      .orderBy(sort[0], sort[1])
      .paginate(page, limit)
    return response.ok(result)
  }

  public async findOne({ response, params: { id } }: HttpContextContract) {
    const result = await Blacklist.query().where('id', id).preload('customer').first()
    if (result == null) {
      return response.notFound()
    }
    return response.ok(result)
  }

  public async create({ response, request }: HttpContextContract) {
    const postSchema = schema.create({
      customer_id: schema.number([
        rules.exists({
          table: 'customers',
          column: 'id',
        }),
      ]),
      email: schema.string.optional({}, [rules.email()]),
      name: schema.string.optional({ trim: true }),
      address: schema.string.optional({ trim: true }),
      apartment: schema.string.optional({ trim: true }),
      company: schema.string.optional({ trim: true }),
      remark: schema.string.optional(),
    })

    try {
      const payload = await request.validate({ schema: postSchema })
      const newBlacklist = await Blacklist.create(payload)

      return response.created({ data: newBlacklist, success: true })
    } catch (e) {
      return response.badRequest(e)
    }
  }

  public async update({ request, response, params: { id } }: HttpContextContract) {
    const blacklist = await Blacklist.find(id)

    if (blacklist == null) {
      return response.notFound()
    }

    const postSchema = schema.create({
      customer_id: schema.number([
        rules.exists({
          table: 'customers',
          column: 'id',
        }),
      ]),
      email: schema.string.optional({}, [rules.email()]),
      name: schema.string.optional({ trim: true }),
      address: schema.string.optional({ trim: true }),
      apartment: schema.string.optional({ trim: true }),
      company: schema.string.optional({ trim: true }),
      remarks: schema.string.optional(),
    })

    try {
      const payload = await request.validate({ schema: postSchema })
      blacklist.merge(payload)
      await blacklist.save()
      return response.ok({ data: blacklist, success: true })
    } catch (e) {
      return response.badRequest(e)
    }
  }

  public async delete({ response, params: { id } }: HttpContextContract) {
    const blacklist = await Blacklist.find(id)

    if (blacklist == null) {
      return response.notFound()
    }
    try {
      await blacklist.delete()
      return response.ok(blacklist)
    } catch (e) {
      return response.badRequest(e)
    }
  }
}
