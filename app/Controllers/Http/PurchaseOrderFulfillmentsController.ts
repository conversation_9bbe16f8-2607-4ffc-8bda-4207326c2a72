import { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import { rules, schema } from '@ioc:Adonis/Core/Validator'
import Database from '@ioc:Adonis/Lucid/Database'
import { PurchaseOrderStatus } from 'App/Models/PurchaseOrder'
import PurchaseOrderFulfillment from 'App/Models/PurchaseOrderFulfillment'

export default class PurchaseOrderFulfillmentsController {
  public async find({ request, response }: HttpContextContract) {
    const page = request.input('page', 1)
    const limit = request.input('limit', 20)
    const sort = request.input('sort', 'created_at:desc').split(':')
    const result = await PurchaseOrderFulfillment.filter(request.all())
      .preload('status')
      .preload('purchaseOrder', (query) => {
        query.preload('items')
      })
      .orderBy(sort[0], sort[1])
      .paginate(page, limit)

    return response.ok(result)
  }

  public async findOne({ response, params: { id } }: HttpContextContract) {
    const fulfillment = await PurchaseOrderFulfillment.query()
      .where('id', id)
      .preload('status')
      .preload('purchaseOrder', (query) => {
        query.preload('items')
      })
      .first()

    if (!fulfillment) {
      return response.notFound()
    }

    return response.ok(fulfillment)
  }

  public async fulfill({ request, response, params: { id } }: HttpContextContract) {
    const postSchema = schema.create({
      line_items: schema.array([rules.minLength(1)]).members(
        schema.object().members({
          line_item_id: schema.number([
            rules.exists({
              table: 'purchase_order_items',
              column: 'id',
            }),
          ]),
          //TODO: make this optional
          quantity: schema.number([rules.range(1, 999)]),
        })
      ),
    })

    try {
      const payload = await request.validate({ schema: postSchema })

      const fulfillment = await PurchaseOrderFulfillment.query()
        .where('id', id)
        .preload('purchaseOrder', (query) => {
          query.preload('items')
        })
        .first()

      if (fulfillment == null) {
        return response.notFound()
      }

      const po = fulfillment.purchaseOrder

      //check if fulfillment was completed
      if (fulfillment.fulfillmentStatusId == 2) {
        return response.unprocessableEntity({ message: 'this fulfillment was already completed' })
      }

      //check duplicate line_item_id
      const valueArr = payload.line_items.map((item) => item.line_item_id)
      const isDuplicate = valueArr.some((item, idx) => valueArr.indexOf(item) != idx)
      if (isDuplicate) {
        return response.badRequest({ message: 'duplicated line_item_id detected' })
      }

      //check if line_items match the requirements
      for (var i = 0; i < payload.line_items.length; i++) {
        const line_item = payload.line_items[i]
        const propertyName = 'line_items.' + i
        const poItem = po.items.find((item) => item.id === line_item.line_item_id)
        if (!poItem) {
          var res: any = {}
          res[propertyName] = [{ line_item_id: 'invalid line_item_id' }]
          return response.badRequest(res)
        }

        if (line_item.quantity != poItem.qty) {
          var res: any = {}
          res[propertyName] = [{ quantity: 'invalid quantity' }]
          return response.badRequest(res)
        }
      }

      //check if po was closed
      if (
        po.status == PurchaseOrderStatus.CANCELLED ||
        po.status == PurchaseOrderStatus.FULFILLED
      ) {
        return response.unprocessableEntity({ message: 'this po has been closed' })
      }

      if (!fulfillment.trackingNumber) {
        return response.internalServerError({
          message: 'this fulfillment does not have a tracking code',
        })
      }

      await Database.transaction(async (trx) => {
        po.status = PurchaseOrderStatus.FULFILLED
        await po.useTransaction(trx).save()
        fulfillment.fulfillmentStatusId = 2
        await fulfillment.useTransaction(trx).save()
      })

      return response.ok({ success: true })
    } catch (e) {
      console.log('order fufillment', e)
      return response.badRequest(e)
    }
  }
}
