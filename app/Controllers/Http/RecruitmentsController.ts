import Mail from '@ioc:Adonis/Addons/Mail'
import { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import { schema } from '@ioc:Adonis/Core/Validator'
import Recruitment from 'App/Models/Recruitment'
import camelcase from 'camelcase'

export default class RecruitmentsController {
  public async find({ request, response }: HttpContextContract) {
    const page = request.input('page', 1)
    const limit = request.input('limit', 20)
    const sort = request.input('sort', 'id:desc').split(':')

    const recruitments = await Recruitment.filter(request.all())
      .orderBy(sort[0], sort[1])
      .paginate(page, limit)

    return response.ok(recruitments)
  }

  public async findOne({ response, params }: HttpContextContract) {
    const recruitment = await await Recruitment.query().where('id', params.id).first()

    if (recruitment != null) {
      return response.ok(recruitment)
    }

    return response.notFound()
  }

  public async create({ request, response }: HttpContextContract) {
    const validationSchema = schema.create({
      first_name: schema.string(),
      last_name: schema.string(),
      email: schema.string(),
      message: schema.string.optional(),
      attachment: schema.string.optional(),
      position: schema.string(),
    })

    try {
      const validationData = await request.validate({
        schema: validationSchema,
      })

      const toCreate = {}

      for (let key of Object.keys(validationData)) {
        toCreate[camelcase(key)] = validationData[key]
      }

      const createRecruitment = await Recruitment.create({
        ...toCreate,
      })

      await Mail.send((message) => {
        message
          .from('<EMAIL>')
          .to('<EMAIL>')
          .subject('You have a new recruitment request!')
          .htmlView('emails/recruitment', {
            firstName: createRecruitment.firstName ?? '',
            lastName: createRecruitment.lastName ?? '',
            email: createRecruitment.email ?? '',
            message: createRecruitment.message ?? '',
            attachment: createRecruitment.attachment ?? '',
            position: createRecruitment.position ?? '',
          })
      })

      return response.created(createRecruitment)
    } catch (e) {
      return response.badRequest(e)
    }
  }
}
