import { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import ProductVariant from 'App/Models/ProductVariant'
import Warehouse from 'App/Models/Warehouse'
import Product from 'App/Models/Product'
import { importProductsFromShopify, syncShopify } from 'App/Modules/ShopifyProductServices'
import Source from 'App/Models/Source'

export default class WebhookProductsController {
  public async create({ request }: HttpContextContract) {
    const shopify = await Source.query().where('source_name', 'shopify').first()
    if (!shopify || !shopify.sync) return

    const shopifyProductId = request.input('id')
    try {
      const defaultWarehouse = await Warehouse.query().where('is_default', true).first()
      if (defaultWarehouse == null) {
        return
      }
      const shopifyVariants = request.input('variants')
      const variants = await ProductVariant.query().whereIn(
        'sku',
        shopifyVariants.map((variant) => variant.sku)
      )

      var nonExistingVariants: any[] = []
      var existingVariants: any[] = []

      for (var i = 0; i < shopifyVariants.length; i++) {
        if (variants.findIndex((item) => item.sku == shopifyVariants[i].sku) != -1) {
          existingVariants.push(shopifyVariants[i])
        } else {
          nonExistingVariants.push(shopifyVariants[i])
        }
      }

      //if same product is found in the databse, sync the value
      if (existingVariants.length > 0) {
        const existingProduct = await Product.query()
          .join('product_variants', 'products.id', 'product_variants.product_id')
          //it wouldnt matter that which variant we choose to based on,
          //since every variant receive from the request will only
          //belong to a single product
          .where('product_variants.sku', variants[0].sku)
          .first()

        if (shopifyProductId !== null && existingProduct !== null) {
          existingProduct.shopifyId = shopifyProductId
          await existingProduct.save()
        }

        await syncShopify(existingVariants)
      }
      //TODO: necessary? since we can just use the import function
      //if it is a totally new product, create it
      if (nonExistingVariants.length > 0) {
      }
    } catch (e) {
      console.log('on product create', e)
    }
  }

  public async update({ request }: HttpContextContract) {
    const shopify = await Source.query().where('source_name', 'shopify').first()
    if (!shopify || !shopify.sync) return

    //mainly focus on cross checking the stock qty
    const product = request.body()
    //const variants = product.variants
    try {
      const currentProduct = await Product.query().where('shopify_id', product.id).first()

      //if the sync of the target product is activated
      //instead to update the shopify product, update the local inventory
      if (currentProduct !== null && currentProduct?.shopifySync) {
        await importProductsFromShopify([product])
      }
      //const updatedInventories = await syncShopify(variants)
    } catch (e) {
      console.log('on product update', e)
    }
  }

  public async delete({ request }: HttpContextContract) {
    console.log(request.body())
  }
}
