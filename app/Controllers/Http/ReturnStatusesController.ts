import { schema, rules } from '@ioc:Adonis/Core/Validator'
import { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import ReturnStatus from 'App/Models/ReturnStatus'
import Event from 'App/Models/Event'

export default class ReturnStatusesController {
  public async find({ request, response }: HttpContextContract) {
    const page = request.input('page', 1)
    const limit = request.input('limit', 20)
    const sort = request.input('sort', 'id:desc').split(':')

    const result = await ReturnStatus.filter(request.all())
      .orderBy(sort[0], sort[1])
      .preload('event', (query) => {
        query.preload('stock', (query) => {
          query.preload('warehouse').preload('variant', (query) => {
            query.preload('product')
          })
        })
      })
      .paginate(page, limit)

    return response.ok(result)
  }

  public async findOne({ response, params: { id } }: HttpContextContract) {
    const result = await ReturnStatus.query()
      .where('id', id)
      .preload('event', (query) => {
        query.preload('stock', (query) => {
          query.preload('warehouse').preload('variant', (query) => {
            query.preload('product')
          })
        })
      })
      .first()
    if (result == null) {
      return response.notFound()
    }
    return response.ok(result)
  }

  public async create({ request, response }: HttpContextContract) {
    const postSchema = schema.create({
      stock_id: schema.number(),
      quantity: schema.number([rules.range(0, 99999)]),
      source_id: schema.number(),
      is_defect: schema.boolean.optional(),
      return_reason: schema.string(),
    })

    try {
      const payload = await request.validate({ schema: postSchema })
      const newEvent = await Event.create({
        stockId: payload.stock_id,
        quantity: payload.quantity,
        //2 refers to the type "return"
        eventTypeId: 2,
        statusId: 2,
        sourceId: payload.source_id,
        remark: payload.return_reason,
      })

      const newReturn = await ReturnStatus.create({
        eventId: newEvent.id,
        isDefect: payload.is_defect,
        returnReason: payload.return_reason,
      })

      return response.created(newReturn)
    } catch (e) {
      return response.badRequest(e)
    }
  }

  public async update({ request, response, params: { id } }: HttpContextContract) {
    const returnStatus = await ReturnStatus.query().where('id', id).preload('event').first()
    if (returnStatus == null) {
      return response.notFound()
    }

    const postSchema = schema.create({
      quantity: schema.number.optional([rules.range(0, 99999)]),
      source_id: schema.number.optional(),
      stock_id: schema.number.optional(),
      is_defect: schema.boolean.optional(),
      return_reason: schema.string.optional(),
    })

    try {
      const payload = await request.validate({ schema: postSchema })

      payload.is_defect ? (returnStatus.isDefect = payload.is_defect) : null
      payload.return_reason ? (returnStatus.returnReason = payload.return_reason) : null
      payload.source_id ? (returnStatus.event.sourceId = payload.source_id) : null
      payload.stock_id ? (returnStatus.event.stockId = payload.stock_id) : null
      payload.quantity ? (returnStatus.event.quantity = payload.quantity) : null

      await returnStatus.save()
      await returnStatus.event.save()

      return response.ok(returnStatus)
    } catch (e) {
      return response.internalServerError(e)
    }
  }
}
