import { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import WeightBasedShippingRule from 'App/Models/WeightBasedShippingRule'
import TrackingCompany from 'App/Models/TrackingCompany'

export default class WeightBasedShippingRuleController {
  public async index({ response }: HttpContextContract) {
    const rules = await WeightBasedShippingRule
      .query()
      .preload('trackingCompany')
      .orderBy('priority', 'asc')
      .orderBy('maxWeight', 'asc')

    return response.ok(rules)
  }

  public async store({ request, response }: HttpContextContract) {
    const data = request.only(['maxWeight', 'trackingCompanyId', 'priority'])

    // Validate tracking company exists
    const trackingCompany = await TrackingCompany.find(data.trackingCompanyId)
    if (!trackingCompany) {
      return response.badRequest({ message: 'Tracking company not found' })
    }

    const rule = await WeightBasedShippingRule.create(data)
    await rule.load('trackingCompany')

    return response.created(rule)
  }

  public async show({ params, response }: HttpContextContract) {
    const rule = await WeightBasedShippingRule
      .query()
      .preload('trackingCompany')
      .where('id', params.id)
      .first()

    if (!rule) {
      return response.notFound({ message: 'Rule not found' })
    }

    return response.ok(rule)
  }

  public async update({ params, request, response }: HttpContextContract) {
    const rule = await WeightBasedShippingRule.find(params.id)
    if (!rule) {
      return response.notFound({ message: 'Rule not found' })
    }

    const data = request.only(['maxWeight', 'trackingCompanyId', 'priority'])

    if (data.trackingCompanyId) {
      const trackingCompany = await TrackingCompany.find(data.trackingCompanyId)
      if (!trackingCompany) {
        return response.badRequest({ message: 'Tracking company not found' })
      }
    }

    rule.merge(data)
    await rule.save()
    await rule.load('trackingCompany')

    return response.ok(rule)
  }

  public async destroy({ params, response }: HttpContextContract) {
    const rule = await WeightBasedShippingRule.find(params.id)
    if (!rule) {
      return response.notFound({ message: 'Rule not found' })
    }

    await rule.delete()
    return response.noContent()
  }
}