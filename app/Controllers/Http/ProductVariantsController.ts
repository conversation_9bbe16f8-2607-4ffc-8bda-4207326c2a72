import { schema } from '@ioc:Adonis/Core/Validator'
import { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import ProductVariant from 'App/Models/ProductVariant'
import { updateOrderStatusWithVariant } from 'App/Modules/OrderServices'

export default class ProductVariantsController {
  public async find({ request, response }: HttpContextContract) {
    const page = request.input('page', 1)
    const limit = request.input('limit', 20)
    const sort = request.input('sort', 'id:desc').split(':')
    const variants = await ProductVariant.filter(request.all())
      .preload('product')
      .preload('stocks')
      .orderBy(`product_variants.${sort[0]}`, sort[1])
      .paginate(page, limit)

    return response.ok(variants)
  }

  public async findOne({ response, params: { id } }: HttpContextContract) {
    try {
      const variant = await ProductVariant.query().where('id', id).first()
      if (variant == null) {
        return response.notFound()
      }

      return response.ok(variant)
    } catch (e) {
      return response.internalServerError(e)
    }
  }

  public async update({ request, response, params: { id } }: HttpContextContract) {
    const variant = await ProductVariant.find(id)

    const postSchema = schema.create({
      variant_name: schema.string.optional({ trim: true }),
      sku: schema.string.optional({ trim: true }),
      shopify_id: schema.string.optional({ trim: true }),
      option_1: schema.string.optional({ trim: true }),
      option_2: schema.string.optional({ trim: true }),
      option_3: schema.string.optional({ trim: true }),
      price: schema.number.optional(),
      weight: schema.number.optional(),
      barcode: schema.string.optional({ trim: true }),
      availability: schema.boolean.optional(),
    })
    if (variant != null) {
      try {
        const payload = await request.validate({ schema: postSchema })
        if (payload.availability) {
          updateOrderStatusWithVariant(variant.id, 1)
        }
        variant.merge(payload)
        await variant.save()
        return response.ok({ data: variant, success: true })
      } catch (e) {
        console.log(e)
        return response.badRequest(e)
      }
    }
    return response.notFound()
  }
}
