import { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import Order from 'App/Models/Order'
import { importOrdersFromShopify } from 'App/Modules/ShopifyOrderServices'

export default class WebhookOrdersController {
  public async onOrderCreation({ request, response }: HttpContextContract) {
    console.log('order create hook')
    try {
      const order: any = request.all()
      await importOrdersFromShopify([order])
    } catch (e) {
      console.log('onOrderCreation', e)
      return response.badRequest(e)
    }
  }

  public async onOrderUpdate({ request, response }: HttpContextContract) {
    console.log('order update hook')

    try {
      const order: any = request.all()
      await importOrdersFromShopify([order])
    } catch (e) {
      console.log('onOrderCreation', e)
      return response.badRequest(e)
    }
  }

  public async onOrderCancelled({ request, response }: HttpContextContract) {
    try {
      const order: any = request.all()
      const localOrder = await Order.query().where('order', order.id).firstOrFail()
      //cancelled
      localOrder.fulfillmentStatusId = 6
      localOrder.transactionStatusId = 4
      await localOrder.save()
    } catch (e) {
      console.log('onOrderCancelled', e)
      return response.badRequest(e)
    }
    return response.ok
  }

  public async onOrderPayment({ request, response }: HttpContextContract) {
    console.log('order paid hook')
    try {
      const order = await Order.query().where('order', request.input('id')).first()

      if (order == null) {
        return response.notFound()
      }

      if (order.transactionStatusId == 1) {
        return response.ok(order)
      }

      //update order status to confirm order
      order.transactionStatusId = 3
      // order.fulfillmentStatusId = 3
      await order.save()
    } catch (e) {
      console.log('onOrderPayment', e)
      return response.badRequest(e)
    }
  }

  public async onOrderCancellation({ request, response }: HttpContextContract) {
    console.log('order cancel hook')
    try {
      const order = await Order.query().where('order', request.input('id')).first()
      if (order == null) {
        return response.notFound()
      }
      //update order status to cancelled
      order.transactionStatusId = 4
      order.fulfillmentStatusId = 6
      await order.save()

      //update event related to the order
      const event = await order[0].related('event').query()

      for (var i = 0; i < event.length; i++) {
        event[i].statusId = 3
        await event[i].save()
      }
    } catch (e) {
      console.log('onOrderCancellation', e)
      return response.badRequest(e)
    }
  }
}
