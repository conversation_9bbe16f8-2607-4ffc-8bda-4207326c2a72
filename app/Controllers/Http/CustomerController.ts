import { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import { schema, rules } from '@ioc:Adonis/Core/Validator'
import Customer from 'App/Models/Customer'
import Order from 'App/Models/Order'

export default class CustomerController {
  public async create({ request, response }: HttpContextContract) {
    const customerSchema = schema.create({
      email: schema.string({ trim: true }, [rules.email()]),
      name: schema.string(),
      phone: schema.string(),
    })

    try {
      const payload = await request.validate({ schema: customerSchema })

      const newCustomer = await Customer.create({
        email: payload.email,
        name: payload.name,
        phone: payload.phone,
      })

      return response.created({ data: newCustomer, success: true })
    } catch (e) {
      return response.badRequest(e)
    }
  }

  public async find({ request, response }: HttpContextContract) {
    const page = request.input('page', 1)
    const limit = request.input('limit', 20)
    const sort = request.input('sort', 'id:desc').split(':')
    const result = await Customer.filter(request.all())
      .orderBy(sort[0], sort[1])
      .paginate(page, limit)
    return response.ok(result)
  }

  public async findOne({ response, params: { id } }: HttpContextContract) {
    const result = await Customer.query().where('id', id).preload('orders').first()
    if (result == null) {
      return response.notFound()
    }

    return response.ok(result)
  }

  public async findByEmail({ response, request }: HttpContextContract) {
    const { email } = request.all()
    const result = await Customer.query().where('email', email).first()
    if (result == null) {
      return response.notFound()
    }

    return response.ok(result)
  }

  public async getOrder({ response, params: { id } }: HttpContextContract) {
    const order = await Order.query()
      .where('customer_id', id)
      .preload('customer')
      .preload('transactionStatus')
      .preload('fulfillmentStatus')
    return response.ok(order)
  }

  public async getProduct({ response, params: { id } }: HttpContextContract) {
    const order = await Order.query().where('customer_id', id)
    const result: any[] = []

    for (var i = 0; i < order.length; i++) {
      const event = await order[i]
        .related('event')
        .query()
        .preload('stock', (query) => {
          query.preload('variant', (query) => {
            query.preload('product')
          })
        })

      //find whether the value is in the array
      //if yes juz add the value
      //if not create a new value
      for (var j = 0; j < event.length; j++) {
        const found = result.find((arr) => arr.name == event[j].stock.variant.fullname)
        if (found == null) {
          const newProduct = {
            name: event[j].stock.variant.fullname,
            quantity: event[j].quantity,
          }
          result.push(newProduct)
        } else {
          for (var k = 0; k < result.length; k++) {
            if (found.name == result[k].name) {
              result[k].quantity = found.quantity + result[k].quantity
            }
          }
        }
      }
    }
    return response.ok(result)
  }

  public async update({ request, response, params: { id } }: HttpContextContract) {
    const customer = await Customer.find(id)
    if (customer == null) {
      return response.notFound()
    }

    const customerSchema = schema.create({
      email: schema.string({ trim: true }, [rules.email()]),
      name: schema.string(),
      phone: schema.string(),
    })

    try {
      const payload = await request.validate({ schema: customerSchema })

      customer.merge(payload)
      customer.save()

      return response.ok({ data: customer, success: true })
    } catch (e) {
      return response.badRequest(e)
    }
  }
}
