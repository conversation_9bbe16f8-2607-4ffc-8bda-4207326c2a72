import { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import { rules, schema } from '@ioc:Adonis/Core/Validator'
import Source from 'App/Models/Source'
import User from 'App/Models/User'

export default class AuthController {
  public async create({ request, response }: HttpContextContract) {
    const validateDataSchema = schema.create({
      email: schema.string({ trim: true }, [
        rules.unique({
          table: 'users',
          column: 'email',
        }),
      ]),
      password: schema.string({ trim: true }, []),
      type: schema.number([rules.range(0, 4)]),
    })

    const validateData = await request.validate({
      schema: validateDataSchema,
    })

    const user = await User.create({
      email: validateData.email,
      password: validateData.password,
    })

    return response.status(201).send(user)
  }

  public async login({ request, auth, response }: HttpContextContract) {
    const validationSchema = schema.create({
      email: schema.string({ trim: true }, [rules.email(), rules.maxLength(255)]),
      password: schema.string({ trim: true }, []),
    })

    const validationData = await request.validate({
      schema: validationSchema,
    })

    const input = {
      email: validationData.email,
      password: validationData.password,
    }

    try {
      const token = await auth.use('api').attempt(input.email, input.password, {
        expiresIn: '3days',
        name: 'Opaque Access Token',
      })

      const user = await User.query().where('id', token.user.id).preload('admin').firstOrFail()

      return response.send({
        message: 'Login successfully',
        success: true,
        jwt: token.toJSON(),
        user: user,
      })
    } catch (error) {
      console.log(error)
      return response.status(400).send({ code: error.code, message: 'Login failed' })
    }
  }

  public async loginAdmin({ request, auth, response }: HttpContextContract) {
    const validationSchema = schema.create({
      email: schema.string({ trim: true }, [rules.email(), rules.maxLength(255)]),
      password: schema.string({ trim: true }, []),
    })

    const validationData = await request.validate({
      schema: validationSchema,
    })

    const input = {
      email: validationData.email,
      password: validationData.password,
    }

    try {
      const token = await auth.use('api').attempt(input.email, input.password, {
        expiresIn: '3days',
      })

      const admin = await User.query().where('id', token.user.id).preload('admin').first()
      const sources = await Source.query().where('was_deleted', false)

      return response.send({
        message: 'Login successfully',
        success: true,
        jwt: token.toJSON(),
        user: admin,
        available_sources: sources.map((source) => source.sourceName),
      })

      // if (admin?.admin !== null) {
      //   if (admin?.admin.type === 0) {
      //     return response.send({
      //       message: 'Login successfully',
      //       success: true,
      //       jwt: token.toJSON(),
      //       user: admin,
      //     })
      //   }
      // } else {
      //   return response.status(400).send({ code: 'not.authorized', message: 'login failed' })
      // }
    } catch (error) {
      console.log(error)
      return response.status(400).send({ code: error.code, message: 'login failed' })
    }
  }

  public async logout({ auth, response }: HttpContextContract) {
    try {
      await auth.logout()
      return response.send({ success: true })
    } catch (error) {
      return response
        .status(400)
        .send({ success: false, code: 'refresh.token.failed', message: 'logout failed' })
    }
  }
}
