import { schema, rules } from '@ioc:Adonis/Core/Validator'
import { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import Feedback from 'App/Models/Feedback'
import FeedbackHistory from 'App/Models/FeedbackHistory'
import Database from '@ioc:Adonis/Lucid/Database'
import Mail from '@ioc:Adonis/Addons/Mail'
import Admin from 'App/Models/Admin'
import { v4 } from 'uuid'
import { DateTime } from 'luxon'

export default class FeedbacksController {
  public async find({ response, request }: HttpContextContract) {
    const page = request.input('page', 1)
    const limit = request.input('limit', 20)
    const sort = request.input('sort', 'created_at:desc').split(':')
    const result = await Feedback.query().orderBy(sort[0], sort[1]).paginate(page, limit)

    return response.ok(result)
  }

  public async findOne({ response, params: { id } }: HttpContextContract) {
    //TODO: add isReaded?
    const result = await Feedback.query()
      .where('id', id)
      .preload('histories', (query) => {
        query.preload('admin')
      })
      .first()

    return response.ok(result)
  }

  //exposed to public access
  public async create({ response, request }: HttpContextContract) {
    //TODO: send notification email
    const postSchema = schema.create({
      email: schema.string({}, [rules.email()]),
      first_name: schema.string.optional({ trim: true }),
      last_name: schema.string.optional({ trim: true }),
      content: schema.string(),
      title: schema.string.optional(),
    })

    try {
      const payload = await request.validate({ schema: postSchema })
      const feedback = await Feedback.create(payload)
      return response.created({ data: feedback, success: true })
    } catch (e) {
      return response.badRequest(e)
    }
  }

  public async getHistories({ response, params: { id } }: HttpContextContract) {
    const histories = await FeedbackHistory.query().where('feedback_id', id).preload('admin')
    return response.ok(histories)
  }

  //exposed to public access
  public async customerCreateHistory({ request, response, params: { id } }: HttpContextContract) {
    const feedback = await Feedback.find(id)
    if (feedback) {
      const postSchema = schema.create({
        content: schema.string(),
      })

      try {
        const payload = await request.validate({ schema: postSchema })
        const result = await FeedbackHistory.create({
          feedbackId: id,
          content: payload.content,
        })
        return response.created({ data: result, success: true })
      } catch (e) {
        return response.badRequest(e)
      }
    }
    return response.notFound({ message: 'feedback was not found' })
  }

  public async customerFindOne({ response, params: { id } }: HttpContextContract) {
    const feedback = await Feedback.query()
      .where('id', id)
      .preload('histories', (query) => {
        query.preload('admin')
      })
      .first()
    if (feedback) {
      return response.ok(feedback)
    }
    return response.notFound({ message: 'feedback was not found' })
  }

  public async adminCreateHistory({
    request,
    auth,
    response,
    params: { id },
  }: HttpContextContract) {
    const user = auth.use('api').user
    const admin = await Admin.query().where('user_id', user!.id).first()

    if (admin) {
      const postSchema = schema.create({
        content: schema.string(),
      })

      const feedback = await Feedback.query().where('id', id).preload('histories').first()

      if (!feedback) {
        return response.notFound({ message: 'feedback not found' })
      }

      try {
        const payload = await request.validate({ schema: postSchema })
        const history = await Database.transaction(async (trx) => {
          const result = await FeedbackHistory.create(
            {
              feedbackId: id,
              adminId: admin.id,
              content: payload.content,
            },
            {
              client: trx,
            }
          )
          const date = DateTime.now()
          feedback.merge({
            token: v4(),
            expiresAt: date.plus({ days: 14 }),
          })
          feedback.useTransaction(trx).save()

          await Mail.send((message) => {
            message
              .from(process.env.MAIL_USERNAME as string)
              .to(feedback.email)
              .subject(`Re: your feedback submitted at ${feedback.createdAt.toLocaleString()}`)
              .htmlView('emails/feedback_reply', {
                content: payload.content,
                id: feedback.id,
                token: feedback.token,
                histories: feedback.histories,
              })
          })

          return result
        })

        return response.created({ data: history, success: true })
      } catch (e) {
        console.log(e)
        return response.badRequest(e)
      }
    }

    //TODO: add permission for feedback management
    return response.notFound({ message: 'not an admin' })
  }
}
