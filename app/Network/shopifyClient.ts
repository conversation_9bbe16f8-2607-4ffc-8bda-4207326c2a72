import axios, { AxiosError, AxiosRequestConfig, AxiosResponse } from 'axios'

const requestHandler = (request: AxiosRequestConfig) => {
  const adminAccess = process.env.ADMIN_ACCESS!

  if (adminAccess != null && typeof adminAccess !== 'undefined') {
    if (request.headers) {
      request.headers['X-Shopify-Access-Token'] = adminAccess
    }
  }

  return request
}

const successHandler = (response: AxiosResponse) => {
  return response
}

const errorHandler = (error: AxiosError<any>) => {
  return Promise.reject({
    message:
      error.response?.data?.errorCode ??
      error.response?.data?.message ??
      error.response?.data?.errors ??
      error.response?.data?.code ??
      error.response ??
      'Please try again later',
  })
}

const shopifyClient = axios.create({
  baseURL: process.env.SHOPIFY_HOST,
})
shopifyClient.interceptors.request.use(requestHandler)
shopifyClient.interceptors.response.use(
  (response) => successHandler(response),
  (error) => errorHandler(error)
)

export default shopifyClient
