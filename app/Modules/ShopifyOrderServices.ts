import Logger from '@ioc:Adonis/Core/Logger'
import { Exception } from '@adonisjs/core/build/standalone'
import Database from '@ioc:Adonis/Lucid/Database'
import AppSetting from 'App/Models/AppSetting'
import Customer from 'App/Models/Customer'
import Event from 'App/Models/Event'
import Fulfillment from 'App/Models/Fulfillment'
import FulfillmentLineItem from 'App/Models/FulfillmentLineItem'
import LineItem from 'App/Models/LineItem'
import Order from 'App/Models/Order'
import ProductVariant from 'App/Models/ProductVariant'
import Shopify from 'App/Models/Shopify/base'
import Stock from 'App/Models/Stock'
import shopifyClient from 'App/Network/shopifyClient'
import { AxiosResponse } from 'axios'
import _ from 'lodash'
import { DateTime } from 'luxon'
// import translate from 'translate'
import { importProductsFromShopify } from './ShopifyProductServices'
import { getTrackingCompanyForShopify } from 'App/Models/TrackingCompany'
import { translate } from 'App/Modules/Translate'
import { findJpPrefecture } from 'App/Modules/JapanPrefecture'

/**
 *
 * @param shopifyOrders an array of shopify order objects
 * @description import orders from shopify, if the order does not exist, order province translation, create the order, otherwise update the order. updating the order including its line items, fulfillments, order details etc.
 * @returns
 */
export async function importOrdersFromShopify(shopifyOrders: Shopify.Order[]) {
  const updateOrders = new Array<Order>()
  for (var j = 0; j < shopifyOrders.length; j++) {
    const order = shopifyOrders[j]
    const orderStatus = await getOrderStatusWithShopifyOrder(order)
    var financialStatus = 2 //pending
    try {
      switch (order.financial_status) {
        case 'authorized':
          financialStatus = 1
          break

        case 'paid':
          financialStatus = 3
          break

        case 'voided':
          financialStatus = 4
          break

        case 'refunded':
          financialStatus = 5
          break

        case 'partially_refunded':
          financialStatus = 6
          break
      }

      var phone: string = order?.shipping_address?.phone
      if (!phone) phone = order?.billing_address?.phone
      if (!phone) phone = order?.customer?.phone

      if (!phone) {
        console.log(
          `unable to import shopify orders (id:${order.id}) (does not have a phone number)`
        )
        continue
      }

      if (phone.charAt(0) != '0') phone = '0' + phone
      // const hasJaCode = phone.includes('+81')
      const cleanPhone = phone.replace('+81', '').split('-').join('').split(' ').join('')
      const standardizedPhone = [
        cleanPhone.slice(0, 3),
        '-',
        cleanPhone.slice(3, 7),
        '-',
        cleanPhone.slice(7),
      ].join('')

      const customer = await Customer.updateOrCreate(
        {
          email: order.email,
        },
        {
          name: `${order.customer.last_name ?? ''}${order.customer.last_name ? ' ' : ''}${
            order.customer.first_name ?? ''
          }`,
          phone: standardizedPhone,
        }
      )
      const cusId = customer.id

      //Translate the province to japanese

      const originalProvince: string = order.shipping_address.province
      const alphabetRegex = /[a-zA-Z]/

      let province = originalProvince

      if (alphabetRegex.test(originalProvince)) {
        province =
          findJpPrefecture(originalProvince.toLocaleLowerCase())?.name ??
          (await translate(originalProvince, 'ja'))
        Logger.info(`translated ${originalProvince} to : ${province}`)
      }

      switch (originalProvince.toLowerCase()) {
        case 'shimane':
        case '島根':
          province = '島根'
          break
        case 'niigata':
        case '新潟':
          province = '新潟'
          break
        case 'hyōgo':
        case 'hyogo':
        case '兵庫':
          province = '兵庫'
          break
        case 'miyagi':
        case '宮城':
          province = '宮城'
          break
        case 'kanagawa':
        case '神奈川':
          province = '神奈川'
          break
        case 'gifu':
        case '岐阜':
          province = '岐阜'
          break
        case 'tochigi':
          province = '栃木'
          break
        case 'kōchi':
        case 'kochi':
        case '高知':
          province = '高知'
          break
      }

      var provinceTrailing = ''

      if (
        province !== '大阪府' &&
        province !== '京都府' &&
        province !== '東京都' &&
        province !== '北海道' &&
        !province.includes('県')
      ) {
        provinceTrailing = '県'
      }

      if (province === '大阪' || province === '京都') {
        provinceTrailing = '府'
      }

      if (province === '東京') {
        provinceTrailing = '都'
      }

      const createdAt = DateTime.fromJSDate(new Date(order.created_at))
      var localOrder: Order | null = await Order.query()
        .where('platform_order_id', order.id)
        .first()
      const shopifyFulfillment = order.fulfillments
      const trackingCode = _.first(shopifyFulfillment)?.tracking_number ?? ''
      const trackingCompanyName = _.first(shopifyFulfillment)?.tracking_company ?? ''
      const trackingUrl = _.first(shopifyFulfillment)?.tracking_url ?? ''
      const fulfillmentDate = _.first(shopifyFulfillment)?.created_at
      const fulfilledAt = fulfillmentDate
        ? DateTime.fromJSDate(new Date(fulfillmentDate))
        : undefined

      if (localOrder === null) {
        localOrder = await Order.create({
          platformOrderId: _.toString(order.id),
          customerId: cusId,
          firstName: order.shipping_address.first_name ?? '',
          lastName: order.shipping_address.last_name ?? '',
          zip: order.shipping_address.zip,
          address:
            `${province}${provinceTrailing}${order.shipping_address.city}${order.shipping_address.address1}`
              .split(' ')
              .join(''),
          apartment: order.shipping_address.address2,
          company: order.shipping_address.company,
          // orderStatusId: cancelled
          //   ? 7
          //   : fulfilled
          //   ? 5
          //   : financialStatus !== 2
          //   ? defaultFulfillmentStatus
          //   : 1,
          noteObj: order.note_attributes,
          orderStatusId: orderStatus,
          //TODO: should use the date on fulfillment
          fulfilledAt: fulfilledAt,
          trackingCompanyName: trackingCompanyName,
          trackingUrl: trackingUrl,
          transactionStatusId: financialStatus,
          tracking: trackingCode,
          createdAt: createdAt,
          phone: standardizedPhone,
          name: order.name,
          //shopify
          shopPlatformId: 1,
        })
      } else {
        localOrder.merge({
          customerId: cusId,
          firstName: order.shipping_address.first_name ?? '',
          lastName: order.shipping_address.last_name ?? '',
          zip: order.shipping_address.zip,
          address: `${province}${provinceTrailing}${order.shipping_address.city}${order.shipping_address.address1}`,
          apartment: order.shipping_address.address2,
          company: order.shipping_address.company,
          transactionStatusId: financialStatus,
          orderStatusId:
            orderStatus !== 5 && orderStatus !== 6 && orderStatus !== 7 //&& orderStatus !== 8
              ? // && orderStatus !== 2
                localOrder.orderStatusId
              : orderStatus,
          // tracking: localOrder.tracking ?? order.tracking_number ?? '',
          createdAt: createdAt,
          phone: standardizedPhone,
          name: order.name,
          //shopify
          shopPlatformId: 1,
        })
        if (orderStatus === 5 && fulfilledAt) {
          localOrder.fulfilledAt = fulfilledAt
          // localOrder.trackingCompanyName = trackingCompanyName
          // localOrder.trackingUrl = trackingUrl
        }
        // if (cancelled) localOrder.fulfillmentStatusId = 6
        // if (!localOrder.tracking) localOrder.tracking = trackingCode
        await localOrder.save()
      }

      updateOrders.push(localOrder)

      const currentOrder = await Order.query()
        .where('id', localOrder.id)
        .preload('fulfillments')
        .preload('lineItems')
        .preload('event', (query) => {
          query.preload('stock', (query) => {
            query.preload('variant')
          })
        })
        .first()

      for (var i = 0; i < order.line_items.length; i++) {
        var productVariant = await ProductVariant.query()
          .where('sku', order.line_items[i].sku)
          .preload('stocks', (query) => {
            query.preload('warehouse')
          })
          .first()

        //is this really required?
        if (productVariant == null) {
          const res = await shopifyClient.get(`/products/${order.line_items[i].product_id}.json`)
          try {
            await importProductsFromShopify([res.data['product']])

            productVariant = await ProductVariant.query()
              .where('sku', order.line_items[i].sku)
              .preload('stocks', (query) => {
                query.preload('warehouse')
              })
              .first()
          } catch (e) {
            console.log('product import in order import function', e)
            throw new Exception(e)
          }
        }

        //create line item for order if needed
        //TODO: if quantity has changed, need to update as well, need to revise
        if (
          order.line_items[i].fulfillment_status === null &&
          !currentOrder?.lineItems.find(
            (item) =>
              item.sku === order.line_items[i].sku &&
              item.quantity === order.line_items[i].fulfillable_quantity
          )
        ) {
          const shopifyLineItem: Shopify.LineItem = order.line_items[i]
          await LineItem.updateOrCreate(
            {
              orderId: localOrder.id,
              sku: shopifyLineItem.sku,
            },
            {
              // eventId: event?.id ?? undefined,
              name: shopifyLineItem.name,
              platformLineItemId: shopifyLineItem.id.toString(),
              price: parseFloat(shopifyLineItem.price),
              quantity: order.line_items[i].fulfillable_quantity,
              grams: shopifyLineItem.grams,
              totalDiscount: parseInt(shopifyLineItem.total_discount) ?? 0,
              totalDiscountSet: shopifyLineItem.total_discount_set,
              taxable: shopifyLineItem.taxable,
              properties: shopifyLineItem.properties,
              requiresShipping: shopifyLineItem.requires_shipping,
              vendor: shopifyLineItem.vendor,
              giftCard: shopifyLineItem.gift_card,
              productTitle: shopifyLineItem.title,
              variantTitle: shopifyLineItem.variant_title,
              // fulfillableQuantity: shopifyLineItem.fulfillable_quantity,
              taxLines: shopifyLineItem.tax_lines,
              duties: shopifyLineItem.duties,
              // fulfillmentId: fulfillment?.id,
            }
          )
        }

        //TODO: if order was cancelled, mark all events as cancelled/failed?
      }

      //remove line items if needed
      const orderWithLineItem = await Order.query()
        .where('id', localOrder.id)
        .preload('lineItems')
        .first()
      const removedLineItems: Shopify.LineItem[] = order.line_items.filter(
        (item: Shopify.LineItem) =>
          item.fulfillment_status === null && item.fulfillable_quantity === 0
      )
      if (removedLineItems.length > 0 && orderWithLineItem) {
        for (const removedLineItem of removedLineItems) {
          const localItemRemovedItem = orderWithLineItem.lineItems.find(
            (item) => item.sku === removedLineItem.sku
          )
          if (localItemRemovedItem) {
            await localItemRemovedItem.delete()
          }
        }
      }

      const shopifyFulfillments: Shopify.Fulfillment[] = order.fulfillments
      for (const shopifyFulfillment of shopifyFulfillments) {
        var fulfillmentStatus = 1
        switch (shopifyFulfillment.status) {
          case 'pending':
            fulfillmentStatus = 1
            break
          case 'open':
            fulfillmentStatus = 1
            break
          case 'success':
            fulfillmentStatus = 2
            break
          case 'failure':
            fulfillmentStatus = 3
            break
          case 'cancelled':
            fulfillmentStatus = 4
            break
          case 'error':
            fulfillmentStatus = 5
            break
        }
        const fulfillmentCreatedAt = new Date(shopifyFulfillment.created_at)
        const fulfillmentUpdatedAt = new Date(shopifyFulfillment.updated_at)
        const fulfillment = await Fulfillment.updateOrCreate(
          {
            orderId: localOrder.id,
            trackingNumber: shopifyFulfillment?.tracking_number,
          },
          {
            name: shopifyFulfillment?.name,
            trackingCompanyName: shopifyFulfillment?.tracking_company,
            trackingUrl: shopifyFulfillment?.tracking_url,
            createdAt: DateTime.fromJSDate(fulfillmentCreatedAt),
            updatedAt: DateTime.fromJSDate(fulfillmentUpdatedAt),
            fulfillmentStatusId: fulfillmentStatus,
          }
        )

        // await LineItem.query()
        //   .where('order_id', localOrder.id)
        //   .andWhereIn(
        //     'sku',
        //     shopifyFulfillment.line_items.map((item) => item.sku)
        //   )
        //   .update({
        //     fulfillmentId: fulfillment.id,
        //   })

        // const fulfillmentLineItems = await Fulfillment.query()
        //   .where('id', fulfillment.id)
        //   .preload('fulfillmentLineItems', (query) => {
        //     query.preload('lineItem')
        //   })
        //   .first()

        if (fulfillmentStatus === 2) {
          await Database.transaction(async (trx) => {
            for (const shopifyFulfillmentItem of shopifyFulfillment.line_items) {
              const stock = await Stock.query()
                .join('product_variants', 'product_variants.id', '=', 'stocks.variant_id')
                .where('product_variants.sku', shopifyFulfillmentItem.sku)
                .andWhere('stocks.quality_id', 1)
                .first()
              if (!stock?.id) {
                continue
              }

              const lineItem = await LineItem.query()
                .where('sku', shopifyFulfillmentItem.sku)
                .andWhere('order_id', currentOrder!.id)
                .first()
              if (!lineItem?.id) {
                continue
              }

              const event = await Event.create(
                {
                  stockId: stock.id,
                  quantity: -shopifyFulfillmentItem.quantity,
                  //2 refers to the type "return"
                  eventTypeId: 3,
                  statusId: 1,
                  sourceId: 1,
                },
                {
                  client: trx,
                }
              )

              await FulfillmentLineItem.firstOrCreate(
                {
                  fulfillmentId: fulfillment.id,
                  lineItemId: lineItem.id,
                },
                { eventId: event.id },
                { client: trx }
              )
            }
          })
        }
      }
    } catch (e) {
      console.log(`import shopify orders (id:${order.id}) `, e)
      continue
      //keep the import process going
      //throw new Exception(e)
      // create an entry in order error table
    }
  }

  return updateOrders
}

export async function fulfillShopifyOrder(
  fulfillmentId: number,
  fulfillmentItems?: {
    line_item_id: number
    quantity: number
  }[],
  notifyCustomer: boolean = true,
  shopifyLocationId?: string
) {
  const localFulfillment = await Fulfillment.query()
    .where('id', fulfillmentId)
    .preload('order', (query) => {
      query.preload('lineItems', (query) => {
        query.preload('fulfillmentLineItems', (query) => {
          query.preload('event')
        })
      })
    })
    .first()

  if (!localFulfillment) {
    return
  }

  try {
    var locationId = shopifyLocationId
    if (!shopifyLocationId) {
      try {
        const appsetting = await AppSetting.query().preload('shopifySetting').first()
        locationId = appsetting?.shopifySetting.outLocationId
      } catch (e) {
        console.log('Fulfill shopify order fetch location', e)
        throw new Exception('shopify location could not be found : ' + e)
      }
    }

    const {
      data: { fulfillment_orders },
    } = await shopifyClient.get<{ fulfillment_orders: Shopify.FulfillmentOrder[] }>(
      `/orders/${localFulfillment.order.platformOrderId}/fulfillment_orders.json`
    )

    const fulfillmentOrder = fulfillment_orders.find(
      (item) => item.assigned_location.location_id.toString() == locationId
    )

    if (!fulfillmentOrder) {
      throw new Exception(
        `Fulfill shopify order fulfillment order with location_id ${locationId} could not be found`
      )
    }

    // const fulfillmentBody = {
    //   fulfillment: {
    //     location_id: locationId,
    //     tracking_company: localFulfillment.trackingCompanyName,
    //     tracking_number: localFulfillment.trackingNumber,
    //     notify_customer: notifyCustomer,
    //   },
    // }

    const findFulfillments = await Database.transaction(async (trx) => {
      var localEvents: Event[] = []

      //if the fulfillment items were specified, fulfill the items accordingly

      const line_items: { id: number; quantity?: number }[] = []
      const {
        data: { order: shopifyOrder },
      }: AxiosResponse<{ order: Shopify.Order }> = await shopifyClient.get(
        `/orders/${localFulfillment.order.platformOrderId}.json`
      )

      var pendingFulfillmentItems: { line_item_id: number; quantity: number }[] = []
      //if fulfillment items were not specified, fulfill all instead
      if (!fulfillmentItems) {
        shopifyOrder.line_items.forEach((item) => {
          if (item.fulfillable_quantity <= 0) return
          const localLineItem = localFulfillment.order.lineItems.find(
            (lineItem) => lineItem.sku === item.sku
          )
          if (localLineItem) {
            pendingFulfillmentItems.push({
              line_item_id: localLineItem.id,
              quantity: item.fulfillable_quantity,
            })
          }
        })
      }

      if (fulfillmentItems) {
        pendingFulfillmentItems = fulfillmentItems
      }

      for (const fulfillmentItem of pendingFulfillmentItems) {
        const lineItem = await LineItem.find(fulfillmentItem.line_item_id)
        if (!lineItem) continue

        const orderLineItem = shopifyOrder.line_items.find((item) => item.sku === lineItem.sku)
        const matchingLineItem = fulfillmentOrder.line_items.find(
          (item) => item.line_item_id === orderLineItem?.id
        )
        if (
          orderLineItem &&
          matchingLineItem &&
          matchingLineItem.fulfillable_quantity >= fulfillmentItem.quantity
        ) {
          line_items.push({
            id: matchingLineItem.id,
            quantity: fulfillmentItem.quantity,
          })

          lineItem.fulfillmentId = localFulfillment.id
          await lineItem.useTransaction(trx).save()

          //create event and fulfillment item according to the requested qty
          const localFulfillmentLineItem = await FulfillmentLineItem.query()
            .where('fulfillment_id', localFulfillment.id)
            .andWhere('line_item_id', fulfillmentItem.line_item_id)
            .first()

          if (!localFulfillmentLineItem) {
            const stock = await Stock.query()
              .join('product_variants', 'product_variants.id', '=', 'stocks.variant_id')
              .where('product_variants.sku', orderLineItem.sku)
              .andWhere('stocks.quality_id', 1)
              .first()
            //TODO: potential risk
            if (!stock?.id) {
              continue
            }

            const newEvent = new Event()
            newEvent.merge({
              stockId: stock.id,
              quantity: -fulfillmentItem.quantity,
              eventTypeId: 3,
              statusId: 1,
              sourceId: 1,
            })
            await newEvent.useTransaction(trx).save()

            await FulfillmentLineItem.updateOrCreate(
              {
                fulfillmentId: localFulfillment.id,
                lineItemId: fulfillmentItem.line_item_id,
              },
              {
                eventId: newEvent.id,
              },
              {
                client: trx,
              }
            )
          }
        }
      }

      localEvents = await Event.query()
        .join('line_items', 'events.id', '=', 'line_items.event_id')
        .whereIn(
          'line_items.id',
          pendingFulfillmentItems.map((item) => item.line_item_id)
        )

      for (const event of localEvents) {
        event.statusId = 1
        await event.useTransaction(trx).save()
      }

      // const { data: findFulfillments } = await shopifyClient.post(
      //   `/orders/${localFulfillment.order.platformOrderId}/fulfillments.json`,
      //   fulfillmentBody
      // )

      const { data: findFulfillments } = await shopifyClient.post('/fulfillments.json', {
        fulfillment: {
          line_items_by_fulfillment_order: [
            {
              fulfillment_order_id: fulfillmentOrder.id,
              fulfillment_order_line_items: line_items,
            },
          ],
          notify_customer: notifyCustomer,
          tracking_info: {
            company: getTrackingCompanyForShopify(localFulfillment.trackingCompanyName),
            number: localFulfillment.trackingNumber,
          },
        },
      })

      //TODO: optimize this
      //TODO: get fulfillment name from response
      localFulfillment.fulfillmentStatusId = 2
      await localFulfillment.useTransaction(trx).save()

      return findFulfillments
    })

    //confirm the order status from shopify
    try {
      const {
        data: { order: shopifyOrder },
      }: AxiosResponse<{ order: Shopify.Order }> = await shopifyClient.get(
        `/orders/${localFulfillment.order.platformOrderId}.json`
      )
      const orderStatus = await getOrderStatusWithShopifyOrder(shopifyOrder)
      if (shopifyOrder) {
        const fulfillmentDate = new Date()
        const luxonDate = DateTime.fromJSDate(fulfillmentDate)
        localFulfillment.order.orderStatusId = orderStatus
        orderStatus === 5 ? (localFulfillment.order.fulfilledAt = luxonDate) : null
        await localFulfillment.order.save()
      }
    } catch (e) {
      console.log('syncing order status after fulfillment', e)
    }

    return findFulfillments
  } catch (e) {
    console.log('Fulfill shopify order', e)
    throw new Exception(e?.message ? e?.message : e)
  }
}

export async function getOrderStatusWithShopifyOrder(shopifyOrder: Shopify.Order) {
  if (shopifyOrder.cancelled_at !== null) return 7

  //out of stock
  // const appsetting = await AppSetting.query().preload('shopifySetting').first()
  // if (!appsetting?.shopifySetting) {
  //   return Promise.reject({
  //     message: 'update order status : No app setting was found',
  //   })
  // }
  // const shopifyLocationId = appsetting?.shopifySetting.outLocationId
  // const localVariant = await ProductVariant.query().whereIn(
  //   'shopify_id',
  //   shopifyOrder.line_items.map((item) => item.variant_id)
  // )
  // const inventoryIds = localVariant.map((item) => item.shopifyStockId).join(',')
  // const {
  //   data: { inventory_levels },
  // } = await shopifyClient.get<{ inventory_levels: Shopify.InventoryLevel[] }>(
  //   `/inventory_levels.json?inventory_item_ids=${inventoryIds}&location_ids=${shopifyLocationId}`
  // )
  // for (const inventoryLevel of inventory_levels) {
  //   if (inventoryLevel.available <= 0) {
  //     console.log(`order ${shopifyOrder.name} : out of stock`)
  //     return 2
  //   }
  // }

  //TODO: handle refunded orders better
  //refunded
  // if (shopifyOrder.refunds.length > 0) {
  //   return 8
  // }

  //refunded
  if (shopifyOrder.financial_status == 'refunded') {
    return 8
  }

  //pending
  if (shopifyOrder.fulfillments.length === 0) {
    return 1
  }

  //partially fulfilled
  if (shopifyOrder.fulfillment_status === 'partial') {
    return 6
  }

  //shipped
  return 5
}

export async function checkClosedOrders() {
  const localActiveOrders = await Order.query().whereNotIn('order_status_id', [5, 7])
  // .andWhereIn('transaction_status_id', [1, 2, 3])
  const shopifyActiveOrders: any[] = []
  var res: AxiosResponse | null = null
  var rel: string = ''
  var query = 'status=open&limit=250'
  do {
    try {
      res = await shopifyClient.get(`/orders.json?${query}`)
      rel = res?.headers['link']
        ? res?.headers['link'].split(',').find((item) => item.split('; ')[1].includes('next')) ?? ''
        : ''

      if (res?.headers['link'] && rel.includes('next')) {
        const link = res.headers['link']
          .split(',')
          .find((item) => item.split('; ')[1].includes('next'))
        query = link ? link.split('?')[1].split('>')[0] : ''
      }
      const shopifyOrders: any[] = res!.data['orders']
      if (shopifyOrders.length > 0) {
        const filteredOrder = shopifyOrders.filter((item) => {
          const isFulfilled = item?.fulfillments?.length > 0
          const paid = item?.financial_status === 'paid'
          const authorized = item?.financial_status === 'authorized'
          return !isFulfilled && (paid || authorized)
        })
        shopifyActiveOrders.push(...filteredOrder)
      }
    } catch (e) {
      res = null
      console.log('checking for shopify cancelled order ', e)
    }
  } while (res?.headers['link'] && rel.includes('next'))

  const pendingShopifyOrderIds: string[] = []
  for (var i = 0; i < localActiveOrders.length; i++) {
    const order = localActiveOrders[i]
    //if a local order is not found in shopify active order, double check it
    if (
      shopifyActiveOrders.find(
        (item) => item.id.toString() === order.platformOrderId.toString()
      ) === undefined
    ) {
      pendingShopifyOrderIds.push(order.platformOrderId)
    }
  }

  try {
    const {
      data: { orders: shopifyOrders },
    }: AxiosResponse<{ orders: Shopify.Order[] }> = await shopifyClient.get(
      `/orders.json?ids=${pendingShopifyOrderIds.join(',')}&status=any&limit=250`
    )
    await importOrdersFromShopify(shopifyOrders)
  } catch (e) {
    //TODO: log this
    console.log(`importing shopify closed order`, e)
  }
}
