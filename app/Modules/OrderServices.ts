import Order from 'App/Models/Order'

/**
 *
 * @param variantId
 * @param status
 * @description update the status of the orders that contain certain variant
 */
export async function updateOrderStatusWithVariant(variantId: number, status: number) {
  var current = 1
  if (status == 1) {
    current = 2
  }
  const orders = await Order.query().where('fulfillment_status_id', current)
  for (let order of orders) {
    const events = await order
      ?.related('event')
      .query()
      .preload('stock', (query) => {
        query.preload('variant', (query) => {
          query.preload('product')
        })
      })

    for (let event of events) {
      if (event.stock.variant.id == variantId) {
        order.fulfillmentStatusId = status
        await order.save()
      }
    }
  }
}
