export function findJpPrefecture(name: string) {
  const data = [
    { code: 1, name: '北海道', enName: 'hokkaido', area: '北海道', capital: '札幌市' },
    { code: 2, name: '青森県', enName: 'aomori', area: '東北', capital: '青森市' },
    { code: 3, name: '岩手県', enName: 'iwate', area: '東北', capital: '盛岡市' },
    { code: 4, name: '宮城県', enName: 'miyagi', area: '東北', capital: '仙台市' },
    { code: 5, name: '秋田県', enName: 'akita', area: '東北', capital: '秋田市' },
    { code: 6, name: '山形県', enName: 'yamagata', area: '東北', capital: '山形市' },
    { code: 7, name: '福島県', enName: 'fukushima', area: '東北', capital: '福島市' },
    { code: 8, name: '茨城県', enName: 'ibaraki', area: '関東', capital: '水戸市' },
    { code: 9, name: '栃木県', enName: 'tochigi', area: '関東', capital: '宇都宮市' },
    { code: 10, name: '群馬県', enName: 'gunma', area: '関東', capital: '前橋市' },
    { code: 11, name: '埼玉県', enName: 'saitama', area: '関東', capital: 'さいたま市' },
    { code: 12, name: '千葉県', enName: 'chiba', area: '関東', capital: '千葉市' },
    { code: 13, name: '東京都', enName: 'tokyo', area: '関東', capital: '新宿区' },
    { code: 14, name: '神奈川県', enName: 'kanagawa', area: '関東', capital: '横浜市' },
    { code: 15, name: '新潟県', enName: 'niigata', area: '中部', capital: '新潟市' },
    { code: 16, name: '富山県', enName: 'toyama', area: '中部', capital: '富山市' },
    { code: 17, name: '石川県', enName: 'ishikawa', area: '中部', capital: '金沢市' },
    { code: 18, name: '福井県', enName: 'fukui', area: '中部', capital: '福井市' },
    { code: 19, name: '山梨県', enName: 'yamanashi', area: '中部', capital: '甲府市' },
    { code: 20, name: '長野県', enName: 'nagano', area: '中部', capital: '長野市' },
    { code: 21, name: '岐阜県', enName: 'gifu', area: '中部', capital: '岐阜市' },
    { code: 22, name: '静岡県', enName: 'shizuoka', area: '中部', capital: '静岡市' },
    { code: 23, name: '愛知県', enName: 'ehime', area: '中部', capital: '名古屋市' },
    { code: 24, name: '三重県', enName: 'mie', area: '関西', capital: '津市' },
    { code: 25, name: '滋賀県', enName: 'shiga', area: '関西', capital: '大津市' },
    { code: 26, name: '京都府', enName: 'kyoto', area: '関西', capital: '京都市' },
    { code: 27, name: '大阪府', enName: 'osaka', area: '関西', capital: '大阪市' },
    { code: 28, name: '兵庫県', enName: 'hyogo', area: '関西', capital: '神戸市' },
    { code: 29, name: '奈良県', enName: 'nara', area: '関西', capital: '奈良市' },
    { code: 30, name: '和歌山県', enName: 'wakayama', area: '関西', capital: '和歌山市' },
    { code: 31, name: '鳥取県', enName: 'tottori', area: '中国', capital: '鳥取市' },
    { code: 32, name: '島根県', enName: 'shimane', area: '中国', capital: '松江市' },
    { code: 33, name: '岡山県', enName: 'okayama', area: '中国', capital: '岡山市' },
    { code: 34, name: '広島県', enName: 'hiroshima', area: '中国', capital: '広島市' },
    { code: 35, name: '山口県', enName: 'yamaguchi', area: '中国', capital: '山口市' },
    { code: 36, name: '徳島県', enName: 'tokushima', area: '四国', capital: '徳島市' },
    { code: 37, name: '香川県', enName: 'kagawa', area: '四国', capital: '高松市' },
    { code: 38, name: '愛媛県', enName: 'ehime', area: '四国', capital: '松山市' },
    { code: 39, name: '高知県', enName: 'kochi', area: '四国', capital: '高知市' },
    { code: 40, name: '福岡県', enName: 'fukuoka', area: '九州', capital: '福岡市' },
    { code: 41, name: '佐賀県', enName: 'saga', area: '九州', capital: '佐賀市' },
    { code: 42, name: '長崎県', enName: 'nagasaki', area: '九州', capital: '長崎市' },
    { code: 43, name: '熊本県', enName: 'kumamoto', area: '九州', capital: '熊本市' },
    { code: 44, name: '大分県', enName: 'oita', area: '九州', capital: '大分市' },
    { code: 45, name: '宮崎県', enName: 'miyazaki', area: '九州', capital: '宮崎市' },
    { code: 46, name: '鹿児島県', enName: 'kagoshima', area: '九州', capital: '鹿児島市' },
    { code: 47, name: '沖縄県', enName: 'okinawa', area: '九州', capital: '那覇市' },
  ]

  return data.find((e) => e.enName == name)
}
