import { Exception } from '@adonisjs/core/build/standalone'
import Database from '@ioc:Adonis/Lucid/Database'
import Event from 'App/Models/Event'
import Product from 'App/Models/Product'
import ProductVariant from 'App/Models/ProductVariant'
import Source from 'App/Models/Source'
import Stock from 'App/Models/Stock'
import Warehouse from 'App/Models/Warehouse'
import shopifyClient from 'App/Network/shopifyClient'
import { getStockWithQuantityById, getStockWithQuantityBySku } from './GetStockWithQuantity'

/**
 *
 * @param shopifyVariantId variant id on shopify
 * @param quantity new quantity to be syned
 * @param shopifyInventoryId inventory id of the variant on shopify
 * @param shopifyLocationId shopify location id for the inventory on shopify
 * @description adjust shopify inventory availibility e.g. if quantity = 10, will set the available inventory on shopify to 10
 * @returns
 */
export async function adjustShopifyInventory(
  shopifyVariantId: string | number,
  quantity: number,
  shopifyInventoryId?: string | number,
  shopifyLocationId?: string | number
) {
  //ATTENTION: a switch to control whether or not sync is allowed
  const shopify = await Source.query().where('source_name', 'shopify').first()
  if (!shopify || !shopify.sync) return

  const targetProduct = await ProductVariant.query().where('shopify_id', shopifyVariantId).first()
  if (targetProduct != null) {
    try {
      let locationId = targetProduct?.shopifyLocationId ?? shopifyLocationId
      if (!targetProduct.shopifyLocationId && !shopifyLocationId) {
        //if the location id doesnot exist, get from shopify
        const inventoryInfo = await shopifyClient.get(
          `/inventory_levels.json?inventory_item_ids=${targetProduct?.shopifyStockId}`
        )
        locationId = inventoryInfo.data['inventory_levels'][0]['location_id']
        targetProduct.shopifyLocationId = inventoryInfo.data['inventory_levels'][0]['location_id']
        await targetProduct?.save()
      }

      //optional to use manually entered inventoryid and location id
      const updatedVariantInventory = {
        inventory_item_id: targetProduct.shopifyStockId ?? shopifyInventoryId,
        available_adjustment: quantity,
        location_id: locationId,
      }

      const adjustInventoryLevels = await shopifyClient.post(
        `/inventory_levels/adjust.json`,
        updatedVariantInventory
      )

      return adjustInventoryLevels
    } catch (e) {
      //if product doesnt exist on shopify yet, simply ignore it
      // if (e.response.status === 400) {
      //   console.log('product currently doesnt exist on shopify')
      //   return
      // }
      console.log(e.response.data)
      throw new Exception(e.response.data)
    }
  }
}

/**
 *
 * @param shopifyVariants
 * @param syncDetails default to true, if true, sync details information such as description, title, etc to shopify
 * @description sync local inventories (local db) to shopify
 * @returns
 */
export async function syncShopify(shopifyVariants: any[], syncDetails = true) {
  //ATTENTION: a switch to control whether or not sync is allowed
  const shopify = await Source.query().where('source_name', 'shopify').first()
  if (!shopify || !shopify.sync) return

  console.log('sync')

  const stocks = await getStockWithQuantityBySku(shopifyVariants.map((variant) => variant.sku))
  const updatedShopifyInventories: any[] = []
  const updatedShopifyProducts: any[] = []

  for (var i = 0; i < shopifyVariants.length; i++) {
    var matchingStock = stocks.find((stock) => stock.variant.sku == shopifyVariants[i].sku)
    var inventoryItemId = shopifyVariants[i].inventory_item_id

    if (matchingStock != null) {
      const currentProductVariant = matchingStock.variant

      //if the corresponding product does not have a shopify id, update it
      if (
        currentProductVariant.product.shopifyId === null ||
        currentProductVariant.product.shopifyId === ''
      ) {
        currentProductVariant.product.shopifyId = shopifyVariants[i].product_id
        await currentProductVariant.product.save()
      }

      if (matchingStock.totalStocks != shopifyVariants[i].inventory_quantity) {
        //if this is a newly created variant, it might
        //not have the ids from shopify
        //in that case, update them

        //update inventory ids
        if (
          currentProductVariant.shopifyLocationId === null ||
          currentProductVariant.shopifyLocationId === ''
        ) {
          const findInventoryLevel = await shopifyClient.get(
            `/inventory_levels.json?inventory_item_ids=${shopifyVariants[i].inventory_item_id}`
          )
          const locationId = findInventoryLevel.data['inventory_levels'][0]['location_id']

          currentProductVariant.shopifyLocationId = locationId
          currentProductVariant.shopifyStockId = inventoryItemId
        }

        //update the shopifyId
        if (currentProductVariant.shopifyId === null || currentProductVariant.shopifyId === '') {
          currentProductVariant.shopifyId = shopifyVariants[i].id
        }
        await currentProductVariant.save()
        ///////////end of update

        updatedShopifyInventories.push({
          inventory_item_id: inventoryItemId,
          available: matchingStock.totalStocks,
          location_id: currentProductVariant.shopifyLocationId,
        })

        const shopifyProduct = updatedShopifyProducts.find(
          (product) => product.id == shopifyVariants[i].product_id
        )

        const updatedShopifyVariant = {
          id: shopifyVariants[i].id,
          price: currentProductVariant.price,
          option1: currentProductVariant.option1,
          option2: currentProductVariant.option2,
          option3: currentProductVariant.option3,
          title: currentProductVariant.variantName,
          weight: currentProductVariant.weight,
        }

        if (shopifyProduct != null) {
          shopifyProduct.variants.push(updatedShopifyVariant)
        } else {
          updatedShopifyProducts.push({
            id: shopifyVariants[i].product_id,
            variants: [updatedShopifyVariant],
          })
        }
      }
    }

    //if no matching stock, something is wrong, because
    //every product on any retail platforms can only
    //be created if a stock is available in the database
    else {
    }
  }

  //adjust the shopify inventory through API
  if (updatedShopifyInventories.length > 0) {
    try {
      await Promise.all(
        updatedShopifyInventories.map(async (inventory) => {
          await shopifyClient.post(`/inventory_levels/set.json`, inventory)
        })
      )
    } catch (e) {
      throw new Exception(e)
    }
  }

  //sync the variant details on shopify as well
  if (syncDetails) {
    for (let updatedShopifyProduct of updatedShopifyProducts) {
      try {
        await shopifyClient.put(`/products/${updatedShopifyProduct.id}.json`, {
          variants: [...updatedShopifyProduct.variants],
        })
      } catch (e) {
        console.log(e)
      }
    }
  }

  return updatedShopifyInventories
}

/**
 *
 * @param shopifyProducts
 * @description update local product based on shopify inventories
 * @returns
 */
export async function importProductsFromShopify(shopifyProducts: any[]) {
  const updatedProducts: Product[] = []

  if (shopifyProducts.length > 0) {
    for (let shopifyProduct of shopifyProducts) {
      //check of the child variants are already exist in the database
      //if there is, it means the product is exising product, vice versa
      try {
        const shopifySkus = shopifyProduct.variants.map((variant) => {
          if (!variant.sku || variant.sku === '')
            throw new Exception(`the sku for ${shopifyProduct.title} ${variant.title} is empty`)
          return variant.sku
        })
        const localVariants = await ProductVariant.query()
          .whereIn('sku', shopifySkus)
          .preload('product')
        const existingProduct = localVariants.find((variant) => shopifySkus.includes(variant.sku))

        const variants: any[] = shopifyProduct['variants']
        const product =
          existingProduct?.product ??
          (await Product.firstOrCreate(
            {
              shopifyId: shopifyProduct['id'],
            },
            {
              shopifySync: true,
              sku: shopifyProduct['id'],
              productName: shopifyProduct['title'],
              shortDescription: shopifyProduct['body_html'],
              option1: shopifyProduct['options'][0] ? shopifyProduct['options'][0]['name'] : '',
              option2: shopifyProduct['options'][1] ? shopifyProduct['options'][1]['name'] : '',
              option3: shopifyProduct['options'][2] ? shopifyProduct['options'][2]['name'] : '',
            }
          ))

        if (existingProduct != null) {
          product.shopifyId = shopifyProduct['id']
          product.shopifySync = true
          product.sku = shopifyProduct['id']
          product.productName = shopifyProduct['title']
          product.shortDescription = shopifyProduct['body_html']
          product.option1 = shopifyProduct['options'][0] ? shopifyProduct['options'][0]['name'] : ''
          product.option2 = shopifyProduct['options'][1] ? shopifyProduct['options'][1]['name'] : ''
          product.option3 = shopifyProduct['options'][2] ? shopifyProduct['options'][2]['name'] : ''
          await product.save()
        }

        //update the variants
        for (let variant of variants) {
          const currentStock = await Database.transaction(async (trx) => {
            const currentVariant = await ProductVariant.updateOrCreate(
              {
                shopifySku: variant.sku,
              },
              {
                variantName: variant.title,
                sku: variant.sku,
                shopifySku: variant.sku,
                productId: product.id,
                price: variant.price,
                weight: variant.weight,
                option1: variant.option1,
                option2: variant.option2,
                option3: variant.option3,
                shopifyId: variant.id,
                barcode: variant.barcode,
                shopifyStockId: variant.inventory_item_id,
              },
              { client: trx }
            )

            //create new stock if no stocks were found
            const currentStock = await Stock.firstOrCreate(
              {
                variantId: currentVariant.id,
              },
              {
                //will consult the client on warehouse selection
                variantId: currentVariant.id,
                warehouseId: 1,
              },
              { client: trx }
            )

            return currentStock
          })

          const stockQty = await getStockWithQuantityById([currentStock.id.toString()])
          const adjustment = variant.inventory_quantity - stockQty[0].totalStocks
          const warehouse = await Warehouse.find(currentStock.warehouseId)

          //create new event for adjustment if needed
          if (adjustment != 0) {
            await Event.create({
              stockId: currentStock.id,
              quantity: adjustment,
              //7 refers to shopify adjustment
              eventTypeId: 7,
              statusId: 1,
              sourceId: 1,
              remark: `Imported from Shopify product : ${shopifyProduct['title']} (${shopifyProduct['id']}), variant: ${variant.title}`,
              from: `Shopify ${process.env.SHOP}`,
              to: warehouse?.warehouseName ?? '',
            })
          }
        }

        updatedProducts.push(product)
        console.log('product importing process')
      } catch (e) {
        console.log('import shopify products', e, ' skipping...')
        continue
        // return Promise.reject({
        //   message: e?.message ?? e,
        // })
      }
    }
  }
  return updatedProducts
}

export async function createShopifyProduct(product: Product) {
  //ATTENTION: a switch to control whether or not sync is allowed
  const shopify = await Source.query().where('source_name', 'shopify').first()
  if (!shopify || !shopify.sync) return

  console.log('create')

  var shopifyProduct = {
    title: product.productName,
    body_html: product.shortDescription,
  }

  var options: any[] = []
  product.option1
    ? options.push({
        name: product.option1,
      })
    : null
  product.option2
    ? options.push({
        name: product.option2,
      })
    : null
  product.option3
    ? options.push({
        name: product.option3,
      })
    : null

  shopifyProduct['options'] = options

  try {
    const res = await shopifyClient.post(`/products.json`, {
      product: shopifyProduct,
    })
    product.shopifyId = res.data['product']['id']
    await product.save()
    return res.data['product']
  } catch (e) {
    throw new Exception(e)
  }
}

export async function createShopifyVariant(
  shopifyProductId: string,
  variant: ProductVariant,
  initialStock: number
) {
  //ATTENTION: a switch to control whether or not sync is allowed
  const shopify = await Source.query().where('source_name', 'shopify').first()
  if (!shopify || !shopify.sync) return

  console.log('create variant')

  const shopifyVariant = {
    price: variant.price.toFixed(2).toString(),
    sku: variant.sku.toString(),
    option1: variant.option1,
    option2: variant.option2 ?? null,
    option3: variant.option3 ?? null,
    barcode: variant.barcode ?? '',
    weight: variant.weight ? variant.weight.toFixed(2) : 0,
  }

  try {
    const newShopifyVariant = await shopifyClient.post(
      `/products/${shopifyProductId}/variants.json`,
      {
        variant: shopifyVariant,
      }
    )
    const shopifyInventory = await shopifyClient.get(
      `/inventory_levels.json?inventory_item_ids=${newShopifyVariant.data['variant']['inventory_item_id']}`
    )

    variant.shopifyLocationId = shopifyInventory.data['inventory_levels'][0]['location_id']
    variant.shopifyStockId = newShopifyVariant.data['variant']['inventory_item_id']
    variant.shopifyId = newShopifyVariant.data['variant']['id']
    await variant.save()

    await adjustShopifyInventory(
      newShopifyVariant.data['variant']['id'],
      initialStock,
      newShopifyVariant.data['variant']['inventory_item_id'],
      shopifyInventory.data['inventory_levels'][0]['location_id']
    )

    return newShopifyVariant
  } catch (e) {
    console.log(e)
    throw new Exception(e)
  }
}
