import Order from 'App/Models/Order'
import View from '@ioc:Adonis/Core/View'
import { Exception } from '@adonisjs/core/build/standalone'
import sgMail, { MailDataRequired } from '@sendgrid/mail'
import EmailTemplate from 'App/Models/EmailTemplate'
import _ from 'lodash'

export enum CustomerNotifiationEmailType {
  CONFIRMATION = 'confirmation',
  DELIVERY = 'delivery',
}

const CustomerNotificationEmail = async (
  orderId: number,
  template: EmailTemplate,
  toEmail?: string
) => {
  const order = await Order.query()
    .where('id', orderId)
    .preload('customer')
    .preload('lineItems')
    .preload('fulfillments')
    .first()
  if (!order) {
    throw new Exception('unable to send delivery notification email, order not found')
  }

  // const noDatetime =
  //   !order.noteObj.find((item) => item.name === 'date') ||
  //   !order.noteObj.find((item) => item.name === 'time')
  const dateNow = new Date()
  const date =
    order?.noteObj?.find((item) => item.name === 'date' || item.name == '_date')?.value ??
    '指定なし'
  // if (!date) {
  //   if (order.confirmationMailSentAt) {
  //     date = await getLatestDeliveryDate(order.confirmationMailSentAt.toJSDate())
  //   } else {
  //     date = await getLatestDeliveryDate(dateNow)
  //   }
  // }
  const to = toEmail ?? order.customer.email
  const time =
    order?.noteObj?.find((item) => item.name === 'time' || item.name == '_time')?.value ??
    '指定なし'
  const nextDay = new Date(dateNow)
  nextDay.setDate(nextDay.getDate() + 1)
  let lang = 'ja-JP'
  const year = nextDay.toLocaleString(lang, { year: 'numeric' })
  const month = nextDay.toLocaleString(lang, { month: 'numeric' })
  const day = nextDay.toLocaleString(lang, { day: 'numeric' })
  const dayName = nextDay.toLocaleString(lang, { weekday: 'narrow' })
  const nextDayStr = `${year}${month}${day}(${dayName})`

  const targetFulfillment =
    order.fulfillments?.length > 0
      ? order.fulfillments?.sort((a, b) => {
          return a.createdAt.toSeconds() - b.createdAt.toSeconds()
        })[0] ?? null
      : null

  // const logo = Fs.readFileSync(Application.resourcesPath('images/logo.png'), {
  //   encoding: 'base64',
  // })
  var htmlView: string = ''
  var subject: string = template.subject
  const company_url = 'https://casefinite.jp/'
  const signature = `ケースフィニット株式会社
  Ergofiniteカスタマーサポート
  HP: ${company_url}
  ※メールベースのみでのお客様サポートとなっております。`
    .split('\n')
    .map((e) => _.trim(e))
  const items = order.lineItems.map((item) => `${item.name} ${item.quantity}個`)

  const data = {
    // logo,
    customer_name: order.lastName + order.firstName,
    order_name: order.name,
    items,
    date: date,
    time: time,
    next_day: nextDayStr,
    signature,
    //TODO: change to cloud storage
    logo: process.env.BASE_URL + '/casefinite_logo.png',
    tracking_company: targetFulfillment?.trackingCompanyName,
    tracking_number: targetFulfillment?.trackingNumber,
    tracking_url: targetFulfillment?.trackingUrl,
  }

  const propertiesRgx = template.subject.match(/\{\{[^}{]+\}\}/g)

  if (propertiesRgx) {
    const properties = propertiesRgx ? propertiesRgx.map((item) => item.replace(/\{|\}/g, '')) : []

    var content = template.subject
    for (var i = 0; i < properties.length; i++) {
      const property = properties[i]
      //TODO: check custom fields
      const value = order[property]
      content = content.replace(propertiesRgx[i], value ?? '')
    }
    subject = content
  }

  htmlView = await View.render(template.view, data)

  // switch (type) {
  //   case CustomerNotifiationEmailType.DELIVERY:
  //     if (date === '指定なし' || time === '指定なし') {
  //       throw new Exception('no date and time are specified')
  //     }
  //     if (order.deliveryMailSentAt) return
  //     htmlView = await View.render('emails/ergofinite_flow/4', data)
  //     subject = `${self} - ご注文の商品の配送時間について【${order.name}】`
  //     break

  //   //3
  //   // case CustomerNotifiationEmailType.CONFIRMATION:
  //   //   if (order.confirmationMailSentAt) return
  //   //   htmlView = await View.render('emails/ergofinite_flow/3', data)
  //   //   subject = `${self} - 出荷のお知らせ 【${order.name}】`
  //   //   break

  //   case CustomerNotifiationEmailType.CONFIRMATION:
  //     if (order.confirmationMailSentAt) return
  //     htmlView = await View.render('emails/ergofinite_flow/2', data)
  //     subject = `${self} - ご注文確定メール【${order.name}】`
  //     break
  // }

  const message: MailDataRequired = {
    to,
    from: {
      name: process.env.MAIL_ALIAS,
      email: process.env.MAIL_USERNAME!,
    },
    subject,
    html: htmlView,
  }

  sgMail.setApiKey(process.env.SENDGRID_API_KEY!)
  const result = await sgMail.send(message)

  // switch (type) {
  //   case CustomerNotifiationEmailType.DELIVERY:
  //     order.deliveryMailSentAt = DateTime.fromJSDate(dateNow)
  //     await order.save()
  //     break

  //   case CustomerNotifiationEmailType.CONFIRMATION:
  //     order.confirmationMailSentAt = DateTime.fromJSDate(dateNow)
  //     await order.save()
  //     break
  // }
  if (to == order.customer.email) {
    const mailDeliveryObj = { ...order.mailDelivery }
    mailDeliveryObj[template.id.toString()] = {
      delivered: true,
    }
    order.mailDelivery = mailDeliveryObj
    await order.save()
  }

  return result
}

export default CustomerNotificationEmail
