import CSVparser from 'csv-parser'
import IconvLite from 'iconv-lite'
import Fs from 'fs'
import _ from 'lodash'
import chardet from 'chardet'
import ProductVariant from 'App/Models/ProductVariant'
import PurchaseOrder, { PurchaseOrderStatus } from 'App/Models/PurchaseOrder'
import Database from '@ioc:Adonis/Lucid/Database'
import PurchaseOrderItem from 'App/Models/PurchaseOrderItem'
import TrackingCompany from 'App/Models/TrackingCompany'
import PurchaseOrderFulfillment from 'App/Models/PurchaseOrderFulfillment'
import AppSetting from 'App/Models/AppSetting'

/**
 *
 * @param filePath path of csv file to be input
 * @param poData the information of the new purchase order to be created
 * @description create purchase order by a csv, with items to be included to a po
 * @returns
 */
export async function createPurchaseOrderByCsv(
  filePath: string,
  poData: {
    remark?: string
    first_name: string
    last_name?: string
    phone: string
    zip: string
    address: string
    apartment?: string
    company?: string
  }
) {
  let results: {} = {}
  const csv = CSVparser

  return new Promise((resolve) => {
    Fs.readFile(filePath, (err, data) => {
      if (err) {
        console.error('Error reading the file:', err)
        return
      }

      let encoding = chardet.detect(data)
      console.log('Detected encoding:', encoding)

      Fs.createReadStream(filePath)
        .pipe(IconvLite.decodeStream(encoding ?? 'UTF-8'))
        .pipe(csv())
        .on('data', (data) => {
          const sku = data['SKU']
          const qty = data['個数']

          if (!results[sku]) {
            results[sku] = parseInt(qty)
            return
          }

          results[sku] += parseInt(qty)
        })
        .on('end', async () => {
          if (Object.keys(results).length == 0) {
            resolve({
              results: [],
            })
          }

          const po = await Database.transaction(async (trx) => {
            const purchaseOrder = await PurchaseOrder.create(
              {
                status: PurchaseOrderStatus.PENDING,
                ...poData,
              },
              { client: trx }
            )

            for (let key of Object.keys(results)) {
              const sku = key
              const qty = results[key]

              if (!sku || !qty) {
                continue
              }

              const variant = await ProductVariant.query({ client: trx }).where('sku', sku).first()

              if (!variant) {
                continue
              }

              await PurchaseOrderItem.create(
                {
                  variantId: variant.id,
                  purchaseOrderId: purchaseOrder.id,
                  sku: variant.sku,
                  qty: qty,
                },
                { client: trx }
              )
            }

            return purchaseOrder
          })

          resolve({
            results: po,
          })
        })
    })
  })
}

export async function updatePurchaseOrderTrackingByCsv(
  filePath: string,
  trackingCompany: TrackingCompany,
  idColumn?: string
) {
  const results: any[] = []
  const csv = CSVparser
  const purchaseOrders: PurchaseOrder[] = []

  return new Promise((resolve) => {
    Fs.readFile(filePath, (err, data) => {
      if (err) {
        console.error('Error reading the file:', err)
        return
      }

      let encoding = chardet.detect(data)
      console.log('Detected encoding:', encoding)

      Fs.createReadStream(filePath)
        .pipe(IconvLite.decodeStream(encoding ?? 'UTF-8'))
        .pipe(csv())
        .on('data', (data) => results.push(data))
        .on('end', async () => {
          for (var i = 0; i < results.length; i++) {
            //Save tracking number in the database
            let trackingNumber: string | null = null
            let orderNumber: string | null = null

            switch (trackingCompany.id) {
              case 1:
                trackingNumber = results[i]['伝票番号']
                orderNumber = results[i]['お客様管理番号']
                break
              case 2:
                trackingNumber = results[i]['お問い合せ送り状No.']
                orderNumber = results[i]['お客様管理番号']
                break
            }

            if (!trackingNumber || !orderNumber) continue

            const purchaseOrder = await PurchaseOrder.query()
              .where(idColumn ?? 'id', orderNumber)
              .preload('fulfillments')
              .first()

            if (purchaseOrder) {
              if (purchaseOrder.status != PurchaseOrderStatus.PENDING) {
                continue
              }

              const updatedOrder = await Database.transaction(async (trx) => {
                //check if there are active fulfillment that was not yet fulfilled/closed
                //1 means pending
                const pendingFulfillment = purchaseOrder.fulfillments.find(
                  (item) => item.fulfillmentStatusId === 1
                )
                if (pendingFulfillment) {
                  //if has, update the tracking number and skip the create new fulfillment step
                  pendingFulfillment.merge({
                    trackingNumber: trackingNumber ?? undefined,
                    trackingUrl: trackingCompany?.redirectLinkPrefix
                      ? trackingCompany?.redirectLinkPrefix + trackingNumber
                      : '',
                    trackingCompanyName: trackingCompany?.name,
                  })
                  await pendingFulfillment.useTransaction(trx).save()
                  return purchaseOrder
                }

                try {
                  // await order.useTransaction(trx).save()
                  await PurchaseOrderFulfillment.updateOrCreate(
                    {
                      trackingCompanyName: trackingCompany?.name,
                      trackingNumber: trackingNumber ?? undefined,
                      purchaseOrderId: purchaseOrder.id,
                    },
                    {
                      trackingUrl: trackingCompany?.redirectLinkPrefix
                        ? trackingCompany?.redirectLinkPrefix + trackingNumber
                        : '',
                      fulfillmentStatusId: 1,
                    },
                    {
                      client: trx,
                    }
                  )

                  return purchaseOrder
                } catch (e) {
                  console.log(e)
                }
              })

              if (updatedOrder) purchaseOrders.push(updatedOrder)
            }
          }

          resolve({
            results: results,
            purchaseOrders,
          })
        })
    })
  })
}

export async function generatePoCSVData({
  purchaseOrders,
  trackingCompany,
  itemName,
  removeJpPhoneCode,
}: {
  purchaseOrders: PurchaseOrder[]
  trackingCompany: TrackingCompany
  itemName: string
  removeJpPhoneCode?: boolean
}) {
  const appSetting = await AppSetting.query()
    .preload('fulfillmentSetting')
    .preload('yamatoSetting')
    .first()
  const yamatoSetting = appSetting?.yamatoSetting
  const fulfillmentSetting = appSetting?.fulfillmentSetting

  if (!yamatoSetting || !fulfillmentSetting) {
    return null
  }

  const dataCmb: any[] = []
  const utc0Date = new Date()
  const date = new Date(utc0Date.getTime() + 9 * 60 * 60 * 1000)
  const dateArr = date.toISOString().split('T')[0].split('-')

  for (var i = 0; i < purchaseOrders.length; i++) {
    const po = purchaseOrders[i]

    const customerName = `${po.lastName} ${po.firstName}`
    const phone = po.phone
    var formattedPhone = phone ?? ''
    // if (phone?.length > 0 && !payload.remove_jp_phone_code) {
    //   formattedPhone = `+81 ${phone.slice(phone[0] == '0' ? 1 : 0, phone.length)}`
    // }

    const hankakuAddress = `${fulfillmentSetting.province}${fulfillmentSetting.city}${
      fulfillmentSetting!.address1 ?? ''
    }`.replace('　', ' ')

    var data: any = {}

    switch (trackingCompany.id) {
      case 1:
        data = {
          'お客様管理番号': `${po.id}`, //`${order.order}${filteredOrders.length > 1 ? '-' + (j + 1) : ''}`,
          '送り状種別': yamatoSetting.kurijouType,
          '温度区分': '',
          '予備4': '',
          //TODO: not sure what to put here
          '出荷予定日': `${dateArr[0]}/${dateArr[1]}/${dateArr[2]}`,
          '配達指定日': '',
          '配達時間帯区分': yamatoSetting.timezone ?? '',
          '届け先コード': '',
          '届け先電話番号': removeJpPhoneCode ? formattedPhone.replace('+81', '') : formattedPhone, //order.customer.phone
          '届け先電話番号(枝番)': '',
          '届け先郵便番号': `${po.zip}`, //order.billing_address.zip
          '届け先住所': `${po.address?.replace('　', '')}`, //order.billing_address.address1 + order.billing_address.address2
          'お届け先建物名（ｱﾊﾟｰﾄﾏﾝｼｮﾝ名）': po.apartment ?? '',
          '会社・部門名１': po.company,
          '会社・部門名２': '',
          '届け先名(漢字)': customerName, //order.billing_address.last_name + order.billing_address.first_name
          '届け先名(カナ)': '',
          // jconv.toHanKana(
          //   await kuroshiro.convert(customer.name, { to: 'katakana' })
          // ), //katakana version of name
          '敬称': '',
          '依頼主コード': '',
          '依頼主電話番号': `${fulfillmentSetting.phone}`,
          '依頼主電話番号(枝番)': '',
          '依頼主郵便番号': fulfillmentSetting.postal,
          '依頼主住所': hankakuAddress,
          '依頼主建物名（ｱﾊﾟｰﾄﾏﾝｼｮﾝ名）': fulfillmentSetting.address2 ?? '',
          '依頼主名（漢字）': fulfillmentSetting.name,
          '依頼主名(カナ)': '',
          '品名コード１': '',
          '品名１': itemName,
          '品名コード２': '',
          '品名2': '',
          '荷扱い１': yamatoSetting.handling1,
          '荷扱い２': yamatoSetting.handling2,
          '記事': '',
          'コレクト代金引換額(税込)': '',
          'コレクト内消費税額': '',
          '営業所止置き': '',
          '止め置き営業所コード': '',
          '発行枚数': '',
          '個数口枠の印字': '',
          '請求先顧客コード': `${yamatoSetting?.customerNo}`,
          '請求先分類コード': '',
          //default to 01 if the user did not set other values at yamato
          '運賃管理番号': `${yamatoSetting?.unchinNo ?? '01'}`,
        }
        break

      case 2:
        data = {
          'お客様管理番号': `${po.id}`, //`${order.order}${filteredOrders.length > 1 ? '-' + (j + 1) : ''}`,
          '送り状種別': yamatoSetting.kurijouType,
          '温度区分': '',
          '予備4': '',
          '出荷予定日': `${dateArr[0]}/${dateArr[1]}/${dateArr[2]}`,
          '配達指定日': '',
          '配達時間帯区分': yamatoSetting.timezone ?? '',
          '届け先コード': '',
          '届け先電話番号': removeJpPhoneCode ? formattedPhone.replace('+81', '') : formattedPhone, //order.customer.phone
          '届け先電話番号(枝番)': '',
          '届け先郵便番号': `${po.zip}`, //order.billing_address.zip
          '届け先住所': `${po.address?.replace('　', ' ')}`, //order.billing_address.address1 + order.billing_address.address2
          'お届け先建物名（ｱﾊﾟｰﾄﾏﾝｼｮﾝ名）': po.apartment ?? '',
          '会社・部門名１': po.company,
          '会社・部門名２': '',
          '届け先名(漢字)': customerName, //order.billing_address.last_name + order.billing_address.first_name
          '届け先名(カナ)': '',
          // jconv.toHanKana(
          //   await kuroshiro.convert(customer.name, { to: 'katakana' })
          // ), //katakana version of name
          '敬称': '',
          '依頼主コード': '',
          '依頼主電話番号': `${fulfillmentSetting.phone}`,
          '依頼主電話番号(枝番)': '',
          '依頼主郵便番号': fulfillmentSetting.postal,
          '依頼主住所': hankakuAddress,
          '依頼主建物名（ｱﾊﾟｰﾄﾏﾝｼｮﾝ名）': fulfillmentSetting.address2 ?? '',
          '依頼主名（漢字）': fulfillmentSetting.name,
          '依頼主名(カナ)': '',
          '品名コード１': '',
          '品名１': itemName,
          '品名コード２': '',
          '品名2': '',
          '荷扱い１': yamatoSetting.handling1,
          '荷扱い２': yamatoSetting.handling2,
          '記事': '',
          'コレクト代金引換額(税込)': '',
          'コレクト内消費税額': '',
          '営業所止置き': '',
          '止め置き営業所コード': '',
          '発行枚数': '',
          '個数口枠の印字': '',
          '請求先顧客コード': `${yamatoSetting?.customerNo}`,
          '請求先分類コード': '',
          //default to 01 if the user did not set other values at yamato
          '運賃管理番号': `${yamatoSetting?.unchinNo ?? '01'}`,
        }
    }
    dataCmb.push(data)
  }

  return dataCmb
}
