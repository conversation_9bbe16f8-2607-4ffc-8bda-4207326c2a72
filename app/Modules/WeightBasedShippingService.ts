import Order from 'App/Models/Order'
import WeightBasedShippingRule from 'App/Models/WeightBasedShippingRule'
import TrackingCompany from 'App/Models/TrackingCompany'

export interface WeightCalculationResult {
  totalWeight: number
  trackingCompanyId: number
  trackingCompany: TrackingCompany
}

export class WeightBasedShippingService {
  /**
   * Calculate total weight of an order in grams
   */
  public static async calculateOrderWeight(order: Order): Promise<number> {
    if (!order.lineItems) {
      await order.load('lineItems')
    }

    let totalWeight = 0

    for (const lineItem of order.lineItems) {
      if (lineItem.requiresShipping && lineItem.grams) {
        totalWeight += lineItem.grams * lineItem.quantity
      }
    }

    return totalWeight
  }

  /**
   * Get the appropriate tracking company based on order weight
   */
  public static async getTrackingCompanyByWeight(
    totalWeight: number
  ): Promise<{
    trackingCompanyId: number
    trackingCompany: TrackingCompany
  }> {
    // Get all weight-based rules ordered by priority and max weight
    const rules = await WeightBasedShippingRule
      .query()
      .preload('trackingCompany')
      .orderBy('priority', 'asc')
      .orderBy('maxWeight', 'asc')

    if (rules.length === 0) {
      // Fallback to Yamato (id: 1) if no rules configured
      const yamato = await TrackingCompany.find(1)
      if (!yamato) {
        throw new Error('Default tracking company (Yamato) not found')
      }
      return {
        trackingCompanyId: 1,
        trackingCompany: yamato,
      }
    }

    // Find the first rule where order weight is less than or equal to max weight
    for (const rule of rules) {
      if (totalWeight <= rule.maxWeight) {
        return {
          trackingCompanyId: rule.trackingCompanyId,
          trackingCompany: rule.trackingCompany,
        }
      }
    }

    // If weight exceeds all rules, use the highest rule
    const highestRule = rules[rules.length - 1]
    return {
      trackingCompanyId: highestRule.trackingCompanyId,
      trackingCompany: highestRule.trackingCompany,
    }
  }

  /**
   * Get tracking company for an order based on its weight
   */
  public static async getTrackingCompanyForOrder(
    order: Order
  ): Promise<WeightCalculationResult> {
    const totalWeight = await this.calculateOrderWeight(order)
    const { trackingCompanyId, trackingCompany } = await this.getTrackingCompanyByWeight(totalWeight)

    return {
      totalWeight,
      trackingCompanyId,
      trackingCompany,
    }
  }
}