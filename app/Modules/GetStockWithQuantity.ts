import Database from '@ioc:Adonis/Lucid/Database'
import Stock from 'App/Models/Stock'

export async function getStockWithQuantityById(
  stockIds?: Array<string>,
  page?: number,
  limit?: number,
  sort: string = 'id:desc'
) {
  if (stockIds == null || stockIds?.length == 0) {
    const stocks = await Stock.query()
      .andWhere('was_deleted', false)
      .preload('warehouse')
      .preload('quality')
      .preload('variant', (query) => {
        query.preload('product')
      })
      .select('*')
      .select(
        Database.from('events')
          .whereColumn('events.stock_id', 'stocks.id')
          .andWhere('events.status_id', '1')
          .sum('events.quantity')
          .as('total_stocks')
          .groupBy('stocks.id')
      )
      .orderBy(sort[0], sort[1] as any)
      .paginate(page ?? 1, limit ?? 20)
    return stocks
  }

  const stocks = await Stock.query()
    .whereIn('id', stockIds)
    .andWhere('was_deleted', false)
    .preload('warehouse')
    .preload('quality')
    .preload('variant', (query) => {
      query.preload('product')
    })
    .select('*')
    .select(
      Database.from('events')
        .whereColumn('events.stock_id', 'stocks.id')
        .andWhere('events.status_id', '1')
        .sum('events.quantity')
        .as('total_stocks')
        .groupBy('stocks.id')
    )
  return stocks
}

export async function getStockWithQuantityBySku(skus: Array<string>) {
  const stocks = await Stock.query()
    .join('product_variants', 'stocks.variant_id', '=', 'product_variants.id')
    .whereIn('product_variants.sku', skus)
    .andWhere('was_deleted', false)
    .preload('warehouse')
    .preload('variant', (query) => {
      query.preload('product')
    })
    .select('*')
    .select(
      Database.from('events')
        .whereColumn('events.stock_id', 'stocks.id')
        .andWhere('events.status_id', '1')
        .sum('events.quantity')
        .as('total_stocks')
        .groupBy('stocks.id')
    )
  return stocks
}
