import Fulfillment from 'App/Models/Fulfillment'
import CSVparser from 'csv-parser'
import IconvLite from 'iconv-lite'
import Order from 'App/Models/Order'
import Fs from 'fs'
import Database from '@ioc:Adonis/Lucid/Database'
import _ from 'lodash'
import { WeightBasedShippingService } from './WeightBasedShippingService'
import chardet from 'chardet'

/**
 *
 * @param filePath path of csv file to be input
 * @param idColumn the id column that the csv's "お客様管理番号" aka order id is referring to in the local order table (could be id, name, platform_order_id)
 * @description binds the yamato tracking numbers in csv to orders
 * @returns
 */
export async function YamatoCSVTracking(filePath: string, idColumn?: string) {
  const results: any[] = []
  const orders: Order[] = []
  const csv = CSVparser

  return new Promise((resolve) => {
    Fs.readFile(filePath, (err, data) => {
      if (err) {
        console.error('Error reading the file:', err)
        return
      }

      let encoding = chardet.detect(data)
      console.log('Detected encoding:', encoding)

      Fs.createReadStream(filePath)
        .pipe(IconvLite.decodeStream(encoding ?? 'UTF-8'))
        .pipe(csv())
        .on('data', (data) => results.push(data))
        .on('end', async () => {
          // const res = await shopifyClient.get('/locations.json')
          // const shopifyLocations: any[] = res.data['locations']
          // //TODO: location id of casefinite should be allow user control
          // const location = shopifyLocations.find((item) => item.name === '日本倉庫')

          for (var i = 0; i < results.length; i++) {
            //Save tracking number in the database
            const trackingNumber = results[i]['伝票番号']
            if (_.isEmpty(trackingNumber)) continue

            // const order = await Order.find(results[i]['お客様管理番号'])
            const order = await Order.query()
              .where(idColumn ?? 'id', results[i]['お客様管理番号'])
              .preload('fulfillments')
              .preload('lineItems')
              .first()

            if (order) {
              if (order.orderStatusId === 5 || order.orderStatusId === 7) {
                continue
              }

              const updatedOrder = await Database.transaction(async (trx) => {
                //const currentFulfillmentStatus = order.fulfillmentStatusId
                // updateToShipped && (order.fulfillmentStatusId = 5) //shipped

                // order.tracking = trackingNumber
                //yamato_ja
                // order.trackingCompanyName = yamatoCompany?.name ?? ''
                // order.trackingUrl = yamatoCompany?.redirectLinkPrefix
                //   ? yamatoCompany?.redirectLinkPrefix + trackingNumber
                //   : ''

                // Get tracking company based on order weight
                const { trackingCompany } = await WeightBasedShippingService.getTrackingCompanyForOrder(
                  order
                )

                //check if there are active fulfillment that was not yet fulfilled/closed
                //1 means pending
                const pendingFulfillment = order.fulfillments.find(
                  (item) => item.fulfillmentStatusId === 1
                )
                if (pendingFulfillment) {
                  //if has, update the tracking number and skip the create new fulfillment step
                  pendingFulfillment.merge({
                    trackingNumber: trackingNumber,
                    trackingUrl: trackingCompany.redirectLinkPrefix
                      ? trackingCompany.redirectLinkPrefix + trackingNumber
                      : '',
                    trackingCompanyName: trackingCompany.name,
                  })
                  await pendingFulfillment.useTransaction(trx).save()
                  return order
                }

                try {
                  // await order.useTransaction(trx).save()
                  await Fulfillment.updateOrCreate(
                    {
                      trackingCompanyName: trackingCompany.name,
                      trackingNumber: trackingNumber,
                      orderId: order.id,
                    },
                    {
                      trackingUrl: trackingCompany.redirectLinkPrefix
                        ? trackingCompany.redirectLinkPrefix + trackingNumber
                        : '',
                      fulfillmentStatusId: 1,
                    },
                    {
                      client: trx,
                    }
                  )

                  //if the user would like to fulfill all items at the same time
                  // if (updateToShipped && currentFulfillmentStatus != 5) {
                  //   //update event related to the order
                  //   const event = await order.related('event').query()

                  //   for (var j = 0; j < event.length; j++) {
                  //     event[j].statusId = 1
                  //     event[j].useTransaction(trx).save()
                  //   }

                  //   await fulfillShopifyOrder(
                  //     order.order.toString(),
                  //     {
                  //       //TODO: control by settings
                  //       tracking_company: 'ヤマト運輸',
                  //       tracking_number: order.tracking.toString(),
                  //       notify_customer: notify_customer,
                  //     },
                  //     location.id
                  //   )
                  // }

                  return order
                } catch (e) {
                  console.log(e)
                }
              })

              if (updatedOrder) orders.push(updatedOrder)
            }
            //TODO: remove qr generation as it is not needed anymore
            // const url = await QRCode.toDataURL(results[i]['お客様管理番号'])
            // dataURLs.push(url)
          }
          resolve({
            results: results,
            // dataURLs: dataURLs,
            orders: orders,
          })
        })
    })
  })
}