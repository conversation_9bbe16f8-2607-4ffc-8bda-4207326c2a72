import archiver from 'archiver'
import Fs from 'fs'

/**
 *
 * @param filepaths paths of the files to be added to the archive
 * @param dateStr date string
 * @description compress the files listed in filePaths into an archive
 * @returns
 */
export async function archiveFiles(filepaths: string[] = [], dateStr: string) {
  const compressedFilePath = `${process.env.CSV_DIR}/${dateStr}.zip`
  const output = Fs.createWriteStream(compressedFilePath)
  const archive = archiver('zip')

  return new Promise<string>((resolve) => {
    output.on('close', async () => {
      console.log(archive.pointer() + ' total bytes')
      console.log('archiver has been finalized and the output file descriptor has closed.')
      resolve(compressedFilePath)
    })

    output.on('end', () => {
      console.log('Data has been drained')
    })

    archive.on('warning', (err) => {
      console.log(err)
    })

    archive.on('error', (err) => {
      console.log(err)
      return Promise.reject(err)
    })

    archive.pipe(output)

    for (var i = 0; i < filepaths.length; i++) {
      archive.file(`${process.env.CSV_DIR}/${filepaths[i]}`, {
        name: filepaths[i],
      })
    }
    archive.finalize()
  })
}
