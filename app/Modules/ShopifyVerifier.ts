import crypto from 'crypto'
import { RequestContract } from '@ioc:Adonis/Core/Request'

/**
 *
 * @param request original request
 * @description verify validity of a shopify webhook
 * @returns
 */
export const verifyShopifyWebhook = (request: RequestContract) => {
  const body = request.raw()
  const hmac = request.headers()['x-shopify-hmac-sha256']

  const hash = crypto
    .createHmac('sha256', process.env.SHOPIFY_WEBHOOK_SECRET!)
    .update(body ?? '', 'utf8')
    .digest('base64')

  return hmac === hash
}
