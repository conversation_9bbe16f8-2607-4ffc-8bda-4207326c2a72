import TrackingCompany from 'App/Models/TrackingCompany'
import Fulfillment from 'App/Models/Fulfillment'
import CSVparser from 'csv-parser'
import IconvLite from 'iconv-lite'
import Order from 'App/Models/Order'
import Fs from 'fs'
import Database from '@ioc:Adonis/Lucid/Database'
import _ from 'lodash'
import chardet from 'chardet'

/**
 *
 * @param filePath path of csv file to be input
 * @param idColumn the id column that the csv's "お客様管理番号" aka order id is referring to in the local order table (could be id, name, platform_order_id)
 * @description binds the sagawa tracking numbers in csv to orders
 * @returns
 */
export async function SagawaCSVTracking(filePath: string, idColumn?: string) {
  const results: any[] = []
  const orders: Order[] = []
  const csv = CSVparser

  return new Promise((resolve) => {
    Fs.readFile(filePath, (err, data) => {
      if (err) {
        console.error('Error reading the file:', err)
        return
      }

      let encoding = chardet.detect(data)
      console.log('Detected encoding:', encoding)

      Fs.createReadStream(filePath)
        .pipe(IconvLite.decodeStream(encoding ?? 'UTF-8'))
        .pipe(csv())
        .on('data', (data) => results.push(data))
        .on('end', async () => {
          const sagawaCompany = await TrackingCompany.find(2)
          for (var i = 0; i < results.length; i++) {
            //Save tracking number in the database
            const trackingNumber = results[i]['お問い合せ送り状No.']
            if (_.isEmpty(trackingNumber)) continue

            const order = await Order.query()
              .where(idColumn ?? 'id', results[i]['お客様管理番号'])
              .preload('fulfillments')
              .first()

            if (order) {
              if (order.orderStatusId === 5 || order.orderStatusId === 7) {
                continue
              }

              const updatedOrder = await Database.transaction(async (trx) => {
                //check if there are active fulfillment that was not yet fulfilled/closed
                //1 means pending
                const pendingFulfillment = order.fulfillments.find(
                  (item) => item.fulfillmentStatusId === 1
                )
                if (pendingFulfillment) {
                  //if has, update the tracking number and skip the create new fulfillment step
                  pendingFulfillment.merge({
                    trackingNumber: trackingNumber,
                    trackingUrl: sagawaCompany?.redirectLinkPrefix
                      ? sagawaCompany?.redirectLinkPrefix + trackingNumber
                      : '',
                    trackingCompanyName: sagawaCompany?.name,
                  })
                  await pendingFulfillment.useTransaction(trx).save()
                  return order
                }

                try {
                  await Fulfillment.updateOrCreate(
                    {
                      trackingCompanyName: sagawaCompany?.name,
                      trackingNumber: trackingNumber,
                      orderId: order.id,
                    },
                    {
                      trackingUrl: sagawaCompany?.redirectLinkPrefix
                        ? sagawaCompany?.redirectLinkPrefix + trackingNumber
                        : '',
                      fulfillmentStatusId: 1,
                    },
                    {
                      client: trx,
                    }
                  )

                  return order
                } catch (e) {
                  console.log(e)
                }
              })

              if (updatedOrder) orders.push(updatedOrder)
            }
          }
          resolve({
            results: results,
            // dataURLs: dataURLs,
            orders: orders,
          })
        })
    })
  })
}
