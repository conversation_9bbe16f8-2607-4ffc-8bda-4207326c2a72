import Logger from '@ioc:Adonis/Core/Logger'
import { v2 as GoogleTranslate } from '@google-cloud/translate'

/**
 *
 * @param inputString
 * @param to language code
 * @description translate string to desire language
 * @returns
 */
export async function translate(inputString: string, to: string = 'en') {
  const translateClient = new GoogleTranslate.Translate({
    projectId: process.env.GOOGLE_CLOUD_PROJECT_ID,
    credentials: {
      client_email: process.env.GOOGLE_CLOUD_CLIENT_EMAIL,
      private_key: process.env.GOOGLE_CLOUD_PRIVATE_KEY,
    },
  })

  const [translation] = await translateClient.translate(inputString, to).catch((e) => {
    Logger.warn('translation error')
    console.log(e.errors ?? e)
    throw e
  })
  return translation
}
