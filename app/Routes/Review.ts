import Route from '@ioc:Adonis/Core/Route'

export default Route.group(() => {
  Route.group(() => {
    Route.get('/:product_id/reviews', 'ReviewsController.findWithProductId')
    Route.post('/:product_id/reviews', 'ReviewsController.create')
  }).prefix('/products')

  Route.group(() => {
    Route.get('/', 'ReviewsController.find')
    Route.post('/', 'ReviewsController.manualCreate')
    Route.get('/:id', 'ReviewsController.findOne')
    Route.put('/:id', 'ReviewsController.manualUpdate')
    Route.delete('/:id', 'ReviewsController.delete')
  })
    .prefix('/reviews')
    .middleware('acl:reviews_access')
})
