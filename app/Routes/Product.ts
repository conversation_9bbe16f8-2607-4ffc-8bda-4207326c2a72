import Route from '@ioc:Adonis/Core/Route'

export default Route.group(() => {
  Route.group(() => {
    Route.get('/', 'ProductsController.find')
    Route.get('/:id', 'ProductsController.findOne')
    Route.put('/:id', 'ProductsController.update')
    Route.post('/', 'ProductsController.create')
    Route.post('/:id/variants', 'ProductsController.createVariant')
    Route.delete('/:id', 'ProductsController.delete')
  }).prefix('/products')

  Route.group(() => {
    Route.post('/shopify', 'ProductsController.syncShopify')
  }).prefix('/sync')

  Route.group(() => {
    Route.post('/shopify', 'ProductsController.importFromShopify')
    Route.get('/shopify', 'ProductsController.findShopify')
  }).prefix('/import')
}).middleware('acl:products_access')
