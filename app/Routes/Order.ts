import Route from '@ioc:Adonis/Core/Route'

export default Route.group(() => {
  Route.group(() => {
    Route.post('/yamato', 'OrderController.getYamatoCSV')
    Route.post('/sagawa', 'OrderController.getSagawaCSV')
  }).prefix('/csv')

  Route.group(() => {
    Route.post('/', 'OrderController.updateTrackingYamato')
  }).prefix('/yamato-csv')

  Route.group(() => {
    Route.post('/', 'OrderController.updateTrackingSagawa')
  }).prefix('/sagawa-csv')

  Route.group(() => {
    Route.post('/', 'OrderController.updateTrackingSagawaPost')
  }).prefix('/sagawa-post-csv')

  Route.group(() => {
    Route.get('/shopify', 'OrderController.importFromShopify')
    Route.get('/shopify/:id', 'OrderController.importSingleFromShopify')
  }).prefix('/import')

  // Route.group(() => {
  //   Route.get('/:id', 'OrderController.getByStatus')
  // }).prefix('/status')

  // Route.group(() => {
  //   Route.get('/:id', 'OrderController.getByTracking')
  // }).prefix('/tracking')

  // Route.get('/:id/capture', 'OrderController.orderCapture')
  Route.get('/by-tracking', 'OrderController.findByTracking')
  Route.put('/:id/memo', 'OrderController.updateOperatorMemo')
  Route.get('/:id/products', 'OrderController.getOrderProduct')
  Route.put('/:id/fulfill', 'OrderController.orderFulfillment')
  Route.get('/:id/blacklists', 'OrderController.getBlacklist')
  Route.get('/', 'OrderController.find')
  Route.get('/:id', 'OrderController.findOne')
  Route.put('/:id', 'OrderController.updateStatus')

  //email
  Route.put('/:id/send-email', 'OrderEmailsController.send')
  Route.put('/:id/email-preview', 'OrderEmailsController.sendPreview')
})
  .prefix('/orders')
  .middleware('acl:orders_access')
