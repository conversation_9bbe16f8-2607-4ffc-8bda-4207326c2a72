import Route from '@ioc:Adonis/Core/Route'

export default Route.group(() => {
  Route.get('/', 'YamatoSettingsController.find')
  Route.get('/:id', 'YamatoSettingsController.findOne')
  Route.post('/', 'YamatoSettingsController.create')
  Route.put('/:id', 'YamatoSettingsController.update')
  Route.delete('/:id', 'YamatoSettingsController.delete')
}).prefix('/yamato-settings')
//TODO : add middleware
