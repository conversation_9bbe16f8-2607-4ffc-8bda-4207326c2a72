import Route from '@ioc:Adonis/Core/Route'

export default Route.group(() => {
  Route.get('/', 'CustomerController.find')
  Route.get('/by-email', 'CustomerController.findByEmail')
  Route.get('/:id', 'CustomerController.findOne')
  Route.post('/', 'CustomerController.create')
  Route.put('/:id', 'CustomerController.update')
  Route.get('/:id/orders', 'CustomerController.getOrder')
  Route.get('/:id/products', 'CustomerController.getProduct')
})
  .prefix('/customers')
  .middleware('acl:customers_access')
