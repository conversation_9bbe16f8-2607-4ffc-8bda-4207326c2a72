import Route from '@ioc:Adonis/Core/Route'

export default Route.group(() => {
  Route.get('/', 'WarehousesController.find')
  Route.get('/:id', 'WarehousesController.findOne')
  Route.get('default/', 'WarehousesController.getByDefault')
  Route.post('/', 'WarehousesController.create')
  Route.put('/:id', 'WarehousesController.update')
  Route.delete('/:id', 'WarehousesController.delete')
})
  .prefix('/warehouses')
  .middleware('acl:warehouses_access')
