import Route from '@ioc:Adonis/Core/Route'

Route.group(() => {
  Route.get('/', 'WeightBasedShippingRuleController.index')
  Route.post('/', 'WeightBasedShippingRuleController.store')
  Route.get('/:id', 'WeightBasedShippingRuleController.show')
  Route.put('/:id', 'WeightBasedShippingRuleController.update')
  Route.delete('/:id', 'WeightBasedShippingRuleController.destroy')
}).prefix('/weight-based-shipping-rules')