import Route from '@ioc:Adonis/Core/Route'

export default Route.group(() => {
  Route.get('/', 'StocksController.find')
  Route.get('/:id', 'StocksController.findOne')
  Route.post('/', 'StocksController.create')
  Route.put('/:id', 'StocksController.update')
  Route.put('/:id/adjust', 'StocksController.adjust')
  Route.delete('/:id', 'StocksController.delete')
})
  .prefix('/stocks')
  .middleware('acl:stocks_access')
