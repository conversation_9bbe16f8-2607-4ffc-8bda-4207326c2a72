import Route from '@ioc:Adonis/Core/Route'

export default Route.group(() => {
  Route.get('/', 'FulfillmentSettingsController.find')
  Route.get('/:id', 'FulfillmentSettingsController.findOne')
  Route.post('/', 'FulfillmentSettingsController.create')
  Route.put('/:id', 'FulfillmentSettingsController.update')
  Route.delete('/:id', 'FulfillmentSettingsController.delete')
}).prefix('/fulfillment-settings')
//TODO : add middleware
//TODO: add s to route name
