import Route from '@ioc:Adonis/Core/Route'

export default Route.group(() => {
  Route.post('/csv/download', 'PurchaseOrdersController.downloadShippngCsv')
  Route.post('/csv/update-tracking', 'PurchaseOrdersController.updateTrackingByCsv')
  Route.post('/csv/create-po', 'PurchaseOrdersController.createByCsv')

  Route.get('/by-tracking', 'PurchaseOrdersController.findByTracking')
  Route.get('/', 'PurchaseOrdersController.find')
  Route.get('/:po_id', 'PurchaseOrdersController.findOne')
  Route.put('/:po_id', 'PurchaseOrdersController.update')
})
  .prefix('/purchase-orders')
  .middleware('acl:orders_access')
