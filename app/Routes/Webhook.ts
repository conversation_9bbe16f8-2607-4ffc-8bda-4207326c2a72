import Route from '@ioc:Adonis/Core/Route'

export default Route.group(() => {
  Route.group(() => {
    Route.post('create', 'WebhookProductsController.create')
    Route.post('update', 'WebhookProductsController.update')
    Route.post('delete', 'WebhookProductsController.delete')
  }).prefix('products')

  Route.group(() => {
    Route.post('create', 'WebhookOrdersController.onOrderCreation')
    Route.post('cancel', 'WebhookOrdersController.onOrderCancellation')
    Route.post('payment', 'WebhookOrdersController.onOrderPayment')
    Route.post('update', 'WebhookOrdersController.onOrderUpdate')
  }).prefix('orders')

  // Route.group(() => {
  //   Route.post('update', 'WebhookOrderFulfillmentsController.update')
  // }).prefix('order-fulfillments')
})
