import Mail from '@ioc:Adonis/Addons/Mail'
import { BaseTask } from 'adonis5-scheduler/build'
import shopifyClient from 'App/Network/shopifyClient'
import axios from 'axios'

export default class CheckAbandonedCheckout extends BaseTask {
  public static get schedule() {
    // try with every hours
    return '0 * * * *'
  }
  /**
   * Set enable use .lock file for block run retry task
   * Lock file save to `build/tmpTaskLock`
   */
  public static get useLock() {
    return false
  }

  public async handle() {
    const res = await shopifyClient.get('/checkouts.json')
    const checkouts: any[] = res.data['checkouts']
    checkouts.forEach(async (checkout) => {
      try {
        // TODO: setup with ENV
        // TODO: maybe store slug in local db
        const products = await Promise.all(
          await checkout.line_items.map(async (item) => {
            const sanityProduct: any = await axios.get(
              `https://u7rveea4.api.sanity.io/v1/data/query/production?query=${encodeURIComponent(
                `*[_type == 'product' && productID == ${item.product_id}] { 'slug': slug.current }[0]`
              )}`
            )

            return {
              name: `${item.presentment_title} - ${item.presentment_variant_title}`,
              url: `https://en.casefinite.com/products/${sanityProduct.data?.result?.slug}`,
              quantity: item.quantity,
              total: item.price * item.quantity,
            }
          })
        )

        // TODO: fix the reload after submission problem
        await Mail.send((message) => {
          message
            .from(process.env.MAIL_USERNAME as string)
            .to(checkout.email)
            .subject("Don't Miss Out!")
            .htmlView('emails/abandoned_checkout_recovery', {
              username: checkout.customer.firstName + ' ' + checkout.customer.lastName,
              email: checkout.email,
              products: products,
              currency: checkout.presentment_currency,
              checkout_url: checkout.abandoned_checkout_url,
            })
        })
      } catch (e) {
        console.log(e)
      }
    })
  }
}
