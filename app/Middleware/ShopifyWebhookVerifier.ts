import { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import { verifyShopifyWebhook } from 'App/Modules/ShopifyVerifier'

export default class ShopifyWebhookVerifier {
  public async handle({ request, response }: HttpContextContract, next: () => Promise<void>) {
    if (!verifyShopifyWebhook(request)) {
      return response.unauthorized()
    }
    // try {
    //   Logger.info(`shopify webhook verified : ${verifyShopifyWebhook(request)}`)
    // } catch (e) {
    //   Logger.info(`webhook verification error ${e}`)
    // }
    await next()
  }
}
