import Database from '@ioc:Adonis/Lucid/Database'
import { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'

export default class Acl {
  public async handle(
    { auth, response, request }: HttpContextContract,
    next: () => Promise<void>,
    permissions: string[]
  ) {
    //for internal app call
    const headers = request.headers()
    if (headers['access-secret-token']) {
      if (headers['access-secret-token'] === process.env.SECRET) {
        await next()
        return
      }
    }

    const user = auth.use('api')?.user
    if (!user) {
      return response.forbidden({
        code: 'access.not.granted',
      })
    }

    const admin = await Database.query().from('admins').where('user_id', user.id).first()

    for (var i = 0; i < permissions.length; i++) {
      if (!admin[permissions[i]]) {
        return response.forbidden({
          code: 'access.not.granted',
        })
      }
    }

    await next()
  }

  // public async handle(
  //   { auth, response }: HttpContextContract,
  //   next: () => Promise<void>,
  //   allowedRoles: string[]
  // ) {
  //   const user = await auth.use('api')?.user
  //   if (!user) {
  //     return response.forbidden({
  //       code: 'access.not.granted',
  //     })
  //   }

  //   const admin = await Admin.query().where('user_id', user.id).first()
  //   let type = 2
  //   if (admin) {
  //     type = admin.type
  //   }

  //   if (!allowedRoles.includes(role[type])) {
  //     return response.forbidden({
  //       code: 'access.not.granted',
  //     })
  //   }

  //   await next()
  // }
}
