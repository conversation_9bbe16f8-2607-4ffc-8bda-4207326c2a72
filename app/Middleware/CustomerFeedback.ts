import { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import Feedback from 'App/Models/Feedback'
import { DateTime } from 'luxon'

export default class CustomerFeedback {
  public async handle(
    { response, request, params: { id } }: HttpContextContract,
    next: () => Promise<void>
  ) {
    //for internal app call
    const headers = request.headers()
    const token = headers['x-customer-service-access-token']
    if (token) {
      const date = new Date()
      const feedback = await Feedback.query()
        .where('id', id)
        .andWhere('token', token)
        .andWhere('expires_at', '>', DateTime.fromJSDate(date).toSQL())
        .first()

      if (feedback) {
        if (feedback.isClosed) {
          return response.unprocessableEntity({
            message: 'thread has been closed',
          })
        }
        await next()
        return
      }
    }

    return response.forbidden({
      code: 'access.not.granted',
    })
  }
}
